#!/bin/bash

mvn package && java -jar target/paymetv-0.0.1-SNAPSHOT.jar
mvn package
docker build -t paymetv-app:latest .

kubectl apply -f ./src/main/resources/conf2/service.yaml
#kubectl apply -f ./src/main/resources/conf2/service2.yaml
kubectl expose deployment pmtv-app --type=LoadBalancer --name=paymetv-service

sleep 20
kubectl get pods --output=wide
curl http://192.168.0.2:80

#kubectl delete services paymetv-service && kubectl delete deployment pmtv-app

#kubectl apply -f ./src/main/resources/conf4/letsencrypt-pvc.yaml
#kubectl apply -f ./src/main/resources/conf4/certbot-job.yaml
#kubectl expose deployment pmtv-app --type=LoadBalancer --name=paymetv-service


# new dns cert
##mkdir /setup/
##COPY new-dns-challenge.sh /setup/
#cmd setup/new-dns-challenge.sh