(function (a) { a.widget("thomaskahn.smoothDivScroll", { options: { scrollingHotSpotLeftClass: "scrollingHotSpotLeft", scrollingHotSpotRightClass: "scrollingHotSpotRight", scrollableAreaClass: "scrollableArea", scrollWrapperClass: "scrollWrapper", hiddenOnStart: false, ajaxContentURL: "", countOnlyClass: "", startAtElementId: "", hotSpotScrolling: true, hotSpotScrollingStep: 15, hotSpotScrollingInterval: 10, hotSpotMouseDownSpeedBooster: 3, visibleHotSpotBackgrounds: "onstart", hotSpotsVisibleTime: 5000, easingAfterHotSpotScrolling: true, easingAfterHotSpotScrollingDistance: 10, easingAfterHotSpotScrollingDuration: 300, easingAfterHotSpotScrollingFunction: "easeOutQuart", mousewheelScrolling: false, mousewheelScrollingStep: 70, easingAfterMouseWheelScrolling: true, easingAfterMouseWheelScrollingDuration: 300, easingAfterMouseWheelScrollingFunction: "easeOutQuart", manualContinuousScrolling: false, autoScrollingMode: "", autoScrollingDirection: "endlessloopright", autoScrollingStep: 1, autoScrollingInterval: 10, scrollToAnimationDuration: 1000, scrollToEasingFunction: "easeOutQuart" }, _create: function () { var b = this, d = this.options, c = this.element; c.wrapInner("<div class='" + d.scrollableAreaClass + "'>").wrapInner("<div class='" + d.scrollWrapperClass + "'>"); c.prepend("<div class='" + d.scrollingHotSpotLeftClass + "'></div><div class='" + d.scrollingHotSpotRightClass + "'></div>"); c.data("scrollWrapper", c.find("." + d.scrollWrapperClass)); c.data("scrollingHotSpotRight", c.find("." + d.scrollingHotSpotRightClass)); c.data("scrollingHotSpotLeft", c.find("." + d.scrollingHotSpotLeftClass)); c.data("scrollableArea", c.find("." + d.scrollableAreaClass)); c.data("speedBooster", 1); c.data("scrollXPos", 0); c.data("hotSpotWidth", c.data("scrollingHotSpotLeft").innerWidth()); c.data("scrollableAreaWidth", 0); c.data("startingPosition", 0); c.data("rightScrollingInterval", null); c.data("leftScrollingInterval", null); c.data("autoScrollingInterval", null); c.data("hideHotSpotBackgroundsInterval", null); c.data("previousScrollLeft", 0); c.data("pingPongDirection", "right"); c.data("getNextElementWidth", true); c.data("swapAt", null); c.data("startAtElementHasNotPassed", true); c.data("swappedElement", null); c.data("originalElements", c.data("scrollableArea").children(d.countOnlyClass)); c.data("visible", true); c.data("enabled", true); c.data("scrollableAreaHeight", c.data("scrollableArea").height()); c.data("scrollerOffset", c.offset()); c.data("initialAjaxContentLoaded", false); c.data("scrollingHotSpotRight").bind("mousemove", function (g) { var f = g.pageX - (this.offsetLeft + c.data("scrollerOffset").left); c.data("scrollXPos", Math.round((f / c.data("hotSpotWidth")) * d.hotSpotScrollingStep)); if (c.data("scrollXPos") === Infinity) { c.data("scrollXPos", 0) } }); c.data("scrollingHotSpotRight").bind("mouseover", function () { c.data("scrollWrapper").stop(true, false); b.stopAutoScrolling(); c.data("rightScrollingInterval", setInterval(function () { if (c.data("scrollXPos") > 0 && c.data("enabled")) { c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() + (c.data("scrollXPos") * c.data("speedBooster"))); if (d.manualContinuousScrolling) { b._checkContinuousSwapRight() } b._showHideHotSpots() } }, d.hotSpotScrollingInterval)); b._trigger("mouseOverRightHotSpot") }); c.data("scrollingHotSpotRight").bind("mouseout", function () { clearInterval(c.data("rightScrollingInterval")); c.data("scrollXPos", 0); if (d.easingAfterHotSpotScrolling && c.data("enabled")) { c.data("scrollWrapper").animate({ scrollLeft: c.data("scrollWrapper").scrollLeft() + d.easingAfterHotSpotScrollingDistance }, { duration: d.easingAfterHotSpotScrollingDuration, easing: d.easingAfterHotSpotScrollingFunction }) } }); c.data("scrollingHotSpotRight").bind("mousedown", function () { c.data("speedBooster", d.hotSpotMouseDownSpeedBooster) }); a("body").bind("mouseup", function () { c.data("speedBooster", 1) }); c.data("scrollingHotSpotLeft").bind("mousemove", function (g) { var f = ((this.offsetLeft + c.data("scrollerOffset").left + c.data("hotSpotWidth")) - g.pageX); c.data("scrollXPos", Math.round((f / c.data("hotSpotWidth")) * d.hotSpotScrollingStep)); if (c.data("scrollXPos") === Infinity) { c.data("scrollXPos", 0) } }); c.data("scrollingHotSpotLeft").bind("mouseover", function () { c.data("scrollWrapper").stop(true, false); b.stopAutoScrolling(); c.data("leftScrollingInterval", setInterval(function () { if (c.data("scrollXPos") > 0 && c.data("enabled")) { c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() - (c.data("scrollXPos") * c.data("speedBooster"))); if (d.manualContinuousScrolling) { b._checkContinuousSwapLeft() } b._showHideHotSpots() } }, d.hotSpotScrollingInterval)); b._trigger("mouseOverLeftHotSpot") }); c.data("scrollingHotSpotLeft").bind("mouseout", function () { clearInterval(c.data("leftScrollingInterval")); c.data("scrollXPos", 0); if (d.easingAfterHotSpotScrolling && c.data("enabled")) { c.data("scrollWrapper").animate({ scrollLeft: c.data("scrollWrapper").scrollLeft() - d.easingAfterHotSpotScrollingDistance }, { duration: d.easingAfterHotSpotScrollingDuration, easing: d.easingAfterHotSpotScrollingFunction }) } }); c.data("scrollingHotSpotLeft").bind("mousedown", function () { c.data("speedBooster", d.hotSpotMouseDownSpeedBooster) }); c.data("scrollableArea").mousewheel(function (e, g) { if (c.data("enabled") && d.mousewheelScrolling) { e.preventDefault(); b.stopAutoScrolling(); var f = Math.round(d.mousewheelScrollingStep * g); b.move(f) } }); if (d.mousewheelScrolling) { c.data("scrollingHotSpotLeft").add(c.data("scrollingHotSpotRight")).mousewheel(function (e, f) { e.preventDefault() }) } a(window).bind("resize", function () { b._showHideHotSpots(); b._trigger("windowResized") }); if (d.ajaxContentURL.length > 0) { b.changeContent(d.ajaxContentURL, "", "html", "replace") } else { b.recalculateScrollableArea() } if (d.autoScrollingMode !== "always") { switch (d.visibleHotSpotBackgrounds) { case "always": b.showHotSpotBackgrounds(); break; case "onstart": b.showHotSpotBackgrounds(); c.data("hideHotSpotBackgroundsInterval", setTimeout(function () { b.hideHotSpotBackgrounds("slow") }, d.hotSpotsVisibleTime)); break; default: break } } if (d.hiddenOnStart) { b.hide() } a(window).load(function () { if (!(d.hiddenOnStart)) { b.recalculateScrollableArea() } if ((d.autoScrollingMode.length > 0) && !(d.hiddenOnStart)) { b.startAutoScrolling() } }) }, _setOption: function (c, e) { var b = this, f = this.options, d = this.element; f[c] = e; if (c === "hotSpotScrolling") { if (e === true) { b._showHideHotSpots() } else { d.data("scrollingHotSpotLeft").hide(); d.data("scrollingHotSpotRight").hide() } } else { if (c === "autoScrollingStep" || c === "easingAfterHotSpotScrollingDistance" || c === "easingAfterHotSpotScrollingDuration" || c === "easingAfterMouseWheelScrollingDuration") { f[c] = parseInt(e, 10) } else { if (c === "autoScrollingInterval") { f[c] = parseInt(e, 10); b.startAutoScrolling() } } } }, showHotSpotBackgrounds: function (c) { var b = this, d = this.element; if (c !== undefined) { d.data("scrollingHotSpotLeft").add(d.data("scrollingHotSpotRight")).css("opacity", "0.0"); d.data("scrollingHotSpotLeft").addClass("scrollingHotSpotLeftVisible"); d.data("scrollingHotSpotRight").addClass("scrollingHotSpotRightVisible"); d.data("scrollingHotSpotLeft").add(d.data("scrollingHotSpotRight")).fadeTo(c, 0.35) } else { d.data("scrollingHotSpotLeft").addClass("scrollingHotSpotLeftVisible"); d.data("scrollingHotSpotLeft").removeAttr("style"); d.data("scrollingHotSpotRight").addClass("scrollingHotSpotRightVisible"); d.data("scrollingHotSpotRight").removeAttr("style") } b._showHideHotSpots() }, hideHotSpotBackgrounds: function (b) { var c = this.element; if (b !== undefined) { c.data("scrollingHotSpotLeft").fadeTo(b, 0, function () { c.data("scrollingHotSpotLeft").removeClass("scrollingHotSpotLeftVisible") }); c.data("scrollingHotSpotRight").fadeTo(b, 0, function () { c.data("scrollingHotSpotRight").removeClass("scrollingHotSpotRightVisible") }) } else { c.data("scrollingHotSpotLeft").removeClass("scrollingHotSpotLeftVisible").removeAttr("style"); c.data("scrollingHotSpotRight").removeClass("scrollingHotSpotRightVisible").removeAttr("style") } }, _showHideHotSpots: function () { var b = this, c = this.element, d = this.options; if (d.manualContinuousScrolling && d.hotSpotScrolling) { c.data("scrollingHotSpotLeft").show(); c.data("scrollingHotSpotRight").show() } else { if (d.autoScrollingMode !== "always" && d.hotSpotScrolling) { if (c.data("scrollableAreaWidth") <= (c.data("scrollWrapper").innerWidth())) { c.data("scrollingHotSpotLeft").hide(); c.data("scrollingHotSpotRight").hide() } else { if (c.data("scrollWrapper").scrollLeft() === 0) { c.data("scrollingHotSpotLeft").hide(); c.data("scrollingHotSpotRight").show(); b._trigger("scrollerLeftLimitReached"); clearInterval(c.data("leftScrollingInterval")); c.data("leftScrollingInterval", null) } else { if (c.data("scrollableAreaWidth") <= (c.data("scrollWrapper").innerWidth() + c.data("scrollWrapper").scrollLeft())) { c.data("scrollingHotSpotLeft").show(); c.data("scrollingHotSpotRight").hide(); b._trigger("scrollerRightLimitReached"); clearInterval(c.data("rightScrollingInterval")); c.data("rightScrollingInterval", null) } else { c.data("scrollingHotSpotLeft").show(); c.data("scrollingHotSpotRight").show() } } } } else { c.data("scrollingHotSpotLeft").hide(); c.data("scrollingHotSpotRight").hide() } } }, _setElementScrollPosition: function (g, c) { var b = this, d = this.element, f = this.options, e = 0; switch (g) { case "first": d.data("scrollXPos", 0); return true; case "start": if (f.startAtElementId !== "") { if (d.data("scrollableArea").has("#" + f.startAtElementId)) { e = a("#" + f.startAtElementId).position().left; d.data("scrollXPos", e); return true } } return false; case "last": d.data("scrollXPos", (d.data("scrollableAreaWidth") - d.data("scrollWrapper").innerWidth())); return true; case "number": if (!(isNaN(c))) { e = d.data("scrollableArea").children(f.countOnlyClass).eq(c - 1).position().left; d.data("scrollXPos", e); return true } return false; case "id": if (c.length > 0) { if (d.data("scrollableArea").has("#" + c)) { e = a("#" + c).position().left; d.data("scrollXPos", e); return true } } return false; default: return false } }, jumpToElement: function (b, d) { var c = this, e = this.element; if (e.data("enabled")) { if (c._setElementScrollPosition(b, d)) { e.data("scrollWrapper").scrollLeft(e.data("scrollXPos")); c._showHideHotSpots(); switch (b) { case "first": c._trigger("jumpedToFirstElement"); break; case "start": c._trigger("jumpedToStartElement"); break; case "last": c._trigger("jumpedToLastElement"); break; case "number": c._trigger("jumpedToElementNumber", null, { elementNumber: d }); break; case "id": c._trigger("jumpedToElementId", null, { elementId: d }); break; default: break } } } }, scrollToElement: function (c, d) { var b = this, e = this.element, g = this.options, f = false; if (e.data("enabled")) { if (b._setElementScrollPosition(c, d)) { if (e.data("autoScrollingInterval") !== null) { b.stopAutoScrolling(); f = true } e.data("scrollWrapper").stop(true, false); e.data("scrollWrapper").animate({ scrollLeft: e.data("scrollXPos") }, { duration: g.scrollToAnimationDuration, easing: g.scrollToEasingFunction, complete: function () { if (f) { b.startAutoScrolling() } b._showHideHotSpots(); switch (c) { case "first": b._trigger("scrolledToFirstElement"); break; case "start": b._trigger("scrolledToStartElement"); break; case "last": b._trigger("scrolledToLastElement"); break; case "number": b._trigger("scrolledToElementNumber", null, { elementNumber: d }); break; case "id": b._trigger("scrolledToElementId", null, { elementId: d }); break; default: break } } }) } } }, move: function (e) { var b = this, c = this.element, d = this.options; c.data("scrollWrapper").stop(true, true); if ((e < 0 && c.data("scrollWrapper").scrollLeft() > 0) || (e > 0 && c.data("scrollableAreaWidth") > (c.data("scrollWrapper").innerWidth() + c.data("scrollWrapper").scrollLeft()))) { if (d.easingAfterMouseWheelScrolling) { c.data("scrollWrapper").animate({ scrollLeft: c.data("scrollWrapper").scrollLeft() + e }, { duration: d.easingAfterMouseWheelScrollingDuration, easing: d.easingAfterMouseWheelFunction, complete: function () { b._showHideHotSpots(); if (d.manualContinuousScrolling) { if (e > 0) { b._checkContinuousSwapRight() } else { b._checkContinuousSwapLeft() } } } }) } else { c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() + e); b._showHideHotSpots(); if (d.manualContinuousScrolling) { if (e > 0) { b._checkContinuousSwapRight() } else { b._checkContinuousSwapLeft() } } } } }, changeContent: function (f, g, b, d) { var c = this, e = this.element; switch (g) { case "flickrFeed": a.getJSON(f, function (k) { var i = [{ size: "small square", pixels: 75, letter: "_s" }, { size: "thumbnail", pixels: 100, letter: "_t" }, { size: "small", pixels: 240, letter: "_m" }, { size: "medium", pixels: 500, letter: "" }, { size: "medium 640", pixels: 640, letter: "_z" }, { size: "large", pixels: 1024, letter: "_b"}]; var p = []; var l = []; var m = []; var h; var j = k.items.length; var n = 0; if (e.data("scrollableAreaHeight") <= 75) { h = 0 } else { if (e.data("scrollableAreaHeight") <= 100) { h = 1 } else { if (e.data("scrollableAreaHeight") <= 240) { h = 2 } else { if (e.data("scrollableAreaHeight") <= 500) { h = 3 } else { if (e.data("scrollableAreaHeight") <= 640) { h = 4 } else { h = 5 } } } } } a.each(k.items, function (r, s) { o(s, h) }); function o(u, s) { var v = u.media.m; var r = v.replace("_m", i[s].letter); var t = a("<img />").attr("src", r); t.load(function () { if (this.height < e.data("scrollableAreaHeight")) { if ((s + 1) < i.length) { o(u, s + 1) } else { q(this) } } else { q(this) } if (n === j) { switch (b) { case "add": if (d === "first") { e.data("scrollableArea").children(":first").before(p) } else { e.data("scrollableArea").children(":last").after(p) } break; default: e.data("scrollableArea").html(p); break } if (e.data("initialAjaxContentLoaded")) { c.recalculateScrollableArea() } else { e.data("initialAjaxContentLoaded", true) } c._showHideHotSpots(); c._trigger("addedFlickrContent", null, { addedElementIds: l }) } }) } function q(t) { var u = e.data("scrollableAreaHeight") / t.height; var r = Math.round(t.width * u); var s = a(t).attr("src").split("/"); var v = (s.length - 1); s = s[v].split("."); a(t).attr("id", s[0]); a(t).css({ height: e.data("scrollableAreaHeight"), width: r }); l.push(s[0]); p.push(t); n++ } }); break; default: a.get(f, function (h) { switch (b) { case "add": if (d === "first") { e.data("scrollableArea").children(":first").before(h) } else { e.data("scrollableArea").children(":last").after(h) } break; default: e.data("scrollableArea").html(h); break } if (e.data("initialAjaxContentLoaded")) { c.recalculateScrollableArea() } else { e.data("initialAjaxContentLoaded", true) } c._showHideHotSpots(); c._trigger("addedHtmlContent") }) } }, recalculateScrollableArea: function () { var b = 0, f = false, e = this.options, d = this.element, c = this; d.data("scrollableArea").children(e.countOnlyClass).each(function () { if ((e.startAtElementId.length > 0) && ((a(this).attr("id")) === e.startAtElementId)) { d.data("startingPosition", b); f = true } b = b + a(this).outerWidth(true) }); if (!(f)) { d.data("startAtElementId", "") } d.data("scrollableAreaWidth", b); d.data("scrollableArea").width(d.data("scrollableAreaWidth")); d.data("scrollWrapper").scrollLeft(d.data("startingPosition")); d.data("scrollXPos", d.data("startingPosition")) }, stopAutoScrolling: function () { var b = this, c = this.element; if (c.data("autoScrollingInterval") !== null) { clearInterval(c.data("autoScrollingInterval")); c.data("autoScrollingInterval", null); b._showHideHotSpots(); b._trigger("autoScrollingStopped") } }, startAutoScrolling: function () { var b = this, c = this.element, d = this.options; if (c.data("enabled")) { b._showHideHotSpots(); clearInterval(c.data("autoScrollingInterval")); c.data("autoScrollingInterval", null); b._trigger("autoScrollingStarted"); c.data("autoScrollingInterval", setInterval(function () { if (!(c.data("visible")) || (c.data("scrollableAreaWidth") <= (c.data("scrollWrapper").innerWidth()))) { clearInterval(c.data("autoScrollingInterval")); c.data("autoScrollingInterval", null) } else { c.data("previousScrollLeft", c.data("scrollWrapper").scrollLeft()); switch (d.autoScrollingDirection) { case "right": c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() + d.autoScrollingStep); if (c.data("previousScrollLeft") === c.data("scrollWrapper").scrollLeft()) { b._trigger("autoScrollingRightLimitReached"); clearInterval(c.data("autoScrollingInterval")); c.data("autoScrollingInterval", null); b._trigger("autoScrollingIntervalStopped") } break; case "left": c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() - d.autoScrollingStep); if (c.data("previousScrollLeft") === c.data("scrollWrapper").scrollLeft()) { b._trigger("autoScrollingLeftLimitReached"); clearInterval(c.data("autoScrollingInterval")); c.data("autoScrollingInterval", null); b._trigger("autoScrollingIntervalStopped") } break; case "backandforth": if (c.data("pingPongDirection") === "right") { c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() + (d.autoScrollingStep)) } else { c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() - (d.autoScrollingStep)) } if (c.data("previousScrollLeft") === c.data("scrollWrapper").scrollLeft()) { if (c.data("pingPongDirection") === "right") { c.data("pingPongDirection", "left"); b._trigger("autoScrollingRightLimitReached") } else { c.data("pingPongDirection", "right"); b._trigger("autoScrollingLeftLimitReached") } } break; case "endlessloopright": c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() + d.autoScrollingStep); b._checkContinuousSwapRight(); break; case "endlessloopleft": c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() - d.autoScrollingStep); b._checkContinuousSwapLeft(); break; default: break } } }, d.autoScrollingInterval)) } }, _checkContinuousSwapRight: function () { var b = this, c = this.element, d = this.options; if (c.data("getNextElementWidth")) { if ((d.startAtElementId.length > 0) && (c.data("startAtElementHasNotPassed"))) { c.data("swapAt", a("#" + d.startAtElementId).outerWidth(true)); c.data("startAtElementHasNotPassed", false) } else { c.data("swapAt", c.data("scrollableArea").children(":first").outerWidth(true)) } c.data("getNextElementWidth", false) } if (c.data("swapAt") <= c.data("scrollWrapper").scrollLeft()) { c.data("swappedElement", c.data("scrollableArea").children(":first").detach()); c.data("scrollableArea").append(c.data("swappedElement")); var e = c.data("scrollWrapper").scrollLeft(); c.data("scrollWrapper").scrollLeft(e - c.data("swappedElement").outerWidth(true)); c.data("getNextElementWidth", true) } }, _checkContinuousSwapLeft: function () { var b = this, c = this.element, d = this.options; if (c.data("getNextElementWidth")) { if ((d.startAtElementId.length > 0) && (c.data("startAtElementHasNotPassed"))) { c.data("swapAt", a("#" + d.startAtElementId).outerWidth(true)); c.data("startAtElementHasNotPassed", false) } else { c.data("swapAt", c.data("scrollableArea").children(":first").outerWidth(true)) } c.data("getNextElementWidth", false) } if (c.data("scrollWrapper").scrollLeft() === 0) { c.data("swappedElement", c.data("scrollableArea").children(":last").detach()); c.data("scrollableArea").prepend(c.data("swappedElement")); c.data("scrollWrapper").scrollLeft(c.data("scrollWrapper").scrollLeft() + c.data("swappedElement").outerWidth(true)); c.data("getNextElementWidth", true) } }, restoreOriginalElements: function () { var b = this, c = this.element; c.data("scrollableArea").html(c.data("originalElements")); b.recalculateScrollableArea(); b.jumpToElement("first") }, show: function () { var b = this.element; b.data("visible", true); b.show() }, hide: function () { var b = this.element; b.data("visible", false); b.hide() }, enable: function () { var b = this.element; b.data("enabled", true) }, disable: function () { var b = this, c = this.element; b.stopAutoScrolling(); clearInterval(c.data("rightScrollingInterval")); clearInterval(c.data("leftScrollingInterval")); clearInterval(c.data("hideHotSpotBackgroundsInterval")); c.data("enabled", false) }, destroy: function () { var b = this, c = this.element; b.stopAutoScrolling(); clearInterval(c.data("rightScrollingInterval")); clearInterval(c.data("leftScrollingInterval")); clearInterval(c.data("hideHotSpotBackgroundsInterval")); c.data("scrollingHotSpotRight").unbind("mouseover"); c.data("scrollingHotSpotRight").unbind("mouseout"); c.data("scrollingHotSpotRight").unbind("mousedown"); c.data("scrollingHotSpotLeft").unbind("mouseover"); c.data("scrollingHotSpotLeft").unbind("mouseout"); c.data("scrollingHotSpotLeft").unbind("mousedown"); c.data("scrollingHotSpotRight").remove(); c.data("scrollingHotSpotLeft").remove(); c.data("scrollableArea").remove(); c.data("scrollWrapper").remove(); c.html(c.data("originalElements")); a.Widget.prototype.destroy.apply(this, arguments) } }) })(jQuery);