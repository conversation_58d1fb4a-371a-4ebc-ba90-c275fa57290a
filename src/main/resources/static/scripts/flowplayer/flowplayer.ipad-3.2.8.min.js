$f.addPlugin("ipad",function(w){var Q=-1;var x=0;var y=1;var N=2;var C=3;var J=4;var h=5;var g=this;var S=1;var R=false;var G=false;var t=false;var q=0;var P=[];var j;var r=null;var c=0;var d={accelerated:false,autoBuffering:false,autoPlay:true,baseUrl:null,bufferLength:3,connectionProvider:null,cuepointMultiplier:1000,cuepoints:[],controls:{},duration:0,extension:"",fadeInSpeed:1000,fadeOutSpeed:1000,image:false,linkUrl:null,linkWindow:"_self",live:false,metaData:{},originalUrl:null,position:0,playlist:[],provider:"http",scaling:"scale",seekableOnBegin:false,start:0,url:null,urlResolvers:[]};var v=Q;var p=Q;var s=/iPad|iPhone|iPod/i.test(navigator.userAgent);var b=null;function l(W,V,T){if(V){for(key in V){if(key){if(V[key]&&typeof V[key]=="function"&&!T){continue}if(V[key]&&typeof V[key]=="object"&&V[key].length===undefined){var U={};l(U,V[key]);W[key]=U}else{W[key]=V[key]}}}}return W}var z={simulateiDevice:false,controlsSizeRatio:1.5,controls:true,debug:false,validExtensions:/m3u8|pls|mov|m4v|mp4|avi|mp3|m4a|aac|m3u/gi,posterExtensions:/png|jpg/gi};l(z,w);function f(){if(z.debug){if(s){var T=[].splice.call(arguments,0).join(", ");console.log.apply(console,[T])}else{console.log.apply(console,arguments)}}}function k(T){switch(T){case -1:return"UNLOADED";case 0:return"LOADED";case 1:return"UNSTARTED";case 2:return"BUFFERING";case 3:return"PLAYING";case 4:return"PAUSED";case 5:return"ENDED"}return"UNKOWN"}function H(T){var U=$f.fireEvent(g.id(),"onBefore"+T,q);return U!==false}function M(T){T.stopPropagation();T.preventDefault();return false}function K(U,T){if(v==Q&&!T){return}p=v;v=U;B();if(U==C){n()}f(k(U))}function A(){b.fp_stop();R=false;G=false;t=false;K(y);K(y)}var e=null;function n(){if(e){return}console.log("starting tracker");e=setInterval(E,100);E()}function B(){clearInterval(e);e=null}function E(){var U=Math.floor(b.fp_getTime()*10)*100;var V=Math.floor(b.duration*10)*100;var W=(new Date()).time;function T(Z,X){Z=Z>=0?Z:V-Math.abs(Z);for(var Y=0;Y<X.length;Y++){if(X[Y].lastTimeFired>W){X[Y].lastTimeFired=-1}else{if(X[Y].lastTimeFired+500>W){continue}else{if(Z==U||(U-500<Z&&U>Z)){X[Y].lastTimeFired=W;$f.fireEvent(g.id(),"onCuepoint",q,X[Y].fnId,X[Y].parameters)}}}}}$f.each(g.getCommonClip().cuepoints,T);$f.each(P[q].cuepoints,T)}function F(){A();t=true;b.fp_seek(0)}function L(T){}function o(){console.log(b);function T(V){var U={};l(U,d);l(U,g.getCommonClip());l(U,V);if(U.ipadUrl){url=decodeURIComponent(U.ipadUrl)}else{if(U.url){url=U.url}}if(url&&url.indexOf("://")==-1&&U.ipadBaseUrl){url=U.ipadBaseUrl+"/"+url}else{if(url&&url.indexOf("://")==-1&&U.baseUrl){url=U.baseUrl+"/"+url}}U.originalUrl=U.url;U.completeUrl=url;U.extension=U.completeUrl.substr(U.completeUrl.lastIndexOf("."));U.type="video";delete U.index;f("fixed clip",U);return U}b.fp_play=function(W,ab,V,aa){var U=null;var X=true;var Y=true;f("Calling play() "+W,W);if(ab){f("ERROR: inStream clips not yet supported");return}if(W!==undefined){if(typeof W=="number"){if(q>=P.length){return}q=W;W=P[q]}else{if(typeof W=="string"){W={url:W}}b.fp_setPlaylist(W.length!==undefined?W:[W])}var ac=new RegExp(z.validExtensions.source);if(!ac.test(P[q].extension)){if(P.length>1&&q<P.length-1){var Z=new RegExp(z.posterExtensions.source);var aa;if(Z.test(P[q].extension)){aa=P[q].url;console.log("Poster image available with url "+aa)}++q;console.log("Not last clip in the playlist, moving to next one");b.fp_play(q,false,true,aa)}return}W=P[q];U=W.completeUrl;if(W.autoBuffering!==undefined&&W.autoBuffering===false){X=false}if(W.autoPlay===undefined||W.autoPlay===true||V===true){X=true;Y=true}else{Y=false}}else{f("clip was not given, simply calling video.play, if not already buffering");if(v!=N){b.play()}return}f("about to play "+U,X,Y);A();if(U){f("Changing SRC attribute"+U);b.setAttribute("src",U)}if(X){if(!H("Begin")){return false}if(aa){Y=W.autoPlay;b.setAttribute("poster",aa);b.setAttribute("preload","none")}$f.fireEvent(g.id(),"onBegin",q);f("calling video.load()");b.load()}if(Y){f("calling video.play()");b.play()}};b.fp_pause=function(){f("pause called");if(!H("Pause")){return false}b.pause()};b.fp_resume=function(){f("resume called");if(!H("Resume")){return false}b.play()};b.fp_stop=function(){f("stop called");if(!H("Stop")){return false}G=true;b.pause();try{b.currentTime=0}catch(U){}};b.fp_seek=function(U){f("seek called "+U);if(!H("Seek")){return false}var Y=0;var U=U+"";if(U.charAt(U.length-1)=="%"){var V=parseInt(U.substr(0,U.length-1))/100;var X=b.duration;Y=X*V}else{Y=U}try{b.currentTime=Y}catch(W){f("Wrong seek time")}};b.fp_getTime=function(){return b.currentTime};b.fp_mute=function(){f("mute called");if(!H("Mute")){return false}S=b.volume;b.volume=0};b.fp_unmute=function(){if(!H("Unmute")){return false}b.volume=S};b.fp_getVolume=function(){return b.volume*100};b.fp_setVolume=function(U){if(!H("Volume")){return false}b.volume=U/100};b.fp_toggle=function(){f("toggle called");if(g.getState()==h){F();return}if(b.paused){b.fp_play()}else{b.fp_pause()}};b.fp_isPaused=function(){return b.paused};b.fp_isPlaying=function(){return !b.paused};b.fp_getPlugin=function(V){if(V=="canvas"||V=="controls"){var U=g.getConfig();return U.plugins&&U.plugins[V]?U.plugins[V]:null}f("ERROR: no support for "+V+" plugin on iDevices");return null};b.fp_close=function(){K(Q);b.parentNode.removeChild(b);b=null};b.fp_getStatus=function(){var V=0;var W=0;try{V=b.buffered.start();W=b.buffered.end()}catch(U){}return{bufferStart:V,bufferEnd:W,state:v,time:b.fp_getTime(),muted:b.muted,volume:b.fp_getVolume()}};b.fp_getState=function(){return v};b.fp_startBuffering=function(){if(v==y){b.load()}};b.fp_setPlaylist=function(V){f("Setting playlist");q=0;for(var U=0;U<V.length;U++){V[U]=T(V[U])}P=V;$f.fireEvent(g.id(),"onPlaylistReplace",V)};b.fp_addClip=function(V,U){V=T(V);P.splice(U,0,V);$f.fireEvent(g.id(),"onClipAdd",V,U)};b.fp_updateClip=function(V,U){l(P[U],V);return P[U]};b.fp_getVersion=function(){return"3.2.3"};b.fp_isFullscreen=function(){return false};b.fp_toggleFullscreen=function(){if(b.fp_isFullscreen()){b.webkitExitFullscreen()}else{b.webkitEnterFullscreen()}};b.fp_addCuepoints=function(X,V,U){var Z=V==-1?g.getCommonClip():P[V];Z.cuepoints=Z.cuepoints||{};X=X instanceof Array?X:[X];for(var W=0;W<X.length;W++){var aa=typeof X[W]=="object"?(X[W]["time"]||null):X[W];if(aa==null){continue}aa=Math.floor(aa/100)*100;var Y=aa;if(typeof X[W]=="object"){Y=l({},X[W],false);if(Y.time!==undefined){delete Y.time}if(Y.parameters!==undefined){l(Y,Y.parameters,false);delete Y.parameters}}Z.cuepoints[aa]=Z.cuepoints[aa]||[];Z.cuepoints[aa].push({fnId:U,lastTimeFired:-1,parameters:Y})}};$f.each(("toggleFullscreen,stopBuffering,reset,playFeed,setKeyboardShortcutsEnabled,isKeyboardShortcutsEnabled,css,animate,showPlugin,hidePlugin,togglePlugin,fadeTo,invoke,loadPlugin").split(","),function(){var U=this;b["fp_"+U]=function(){f("ERROR: unsupported API on iDevices "+U);return false}})}function I(){var ag=["abort","canplay","canplaythrough","durationchange","emptied","ended","error","loadeddata","loadedmetadata","loadstart","pause","play","playing","progress","ratechange","seeked","seeking","stalled","suspend","volumechange","waiting"];var Y=function(ai){f("Got event "+ai.type,ai)};for(var aa=0;aa<ag.length;aa++){b.addEventListener(ag[aa],Y,false)}var V=function(ai){f("got onBufferEmpty event "+ai.type);K(N);$f.fireEvent(g.id(),"onBufferEmpty",q)};b.addEventListener("emptied",V,false);b.addEventListener("waiting",V,false);var X=function(ai){if(p==y||p==N){}else{f("Restoring old state "+k(p));K(p)}$f.fireEvent(g.id(),"onBufferFull",q)};b.addEventListener("canplay",X,false);b.addEventListener("canplaythrough",X,false);var W=function(aj){var ai;c=P[q].start;if(P[q].duration>0){ai=P[q].duration;r=ai+c}else{ai=b.duration;r=null}b.fp_updateClip({duration:ai,metaData:{duration:b.duration}},q);P[q].duration=b.duration;P[q].metaData={duration:b.duration};$f.fireEvent(g.id(),"onMetaData",q,P[q])};b.addEventListener("loadedmetadata",W,false);b.addEventListener("durationchange",W,false);var U=function(ai){if(r&&b.currentTime>r){b.fp_seek(c);A();return M(ai)}};b.addEventListener("timeupdate",U,false);var af=function(ai){if(v==J){if(!H("Resume")){f("Resume disallowed, pausing");b.fp_pause();return M(ai)}$f.fireEvent(g.id(),"onResume",q)}K(C);if(!R){R=true;$f.fireEvent(g.id(),"onStart",q)}};b.addEventListener("playing",af,false);var T=function(ai){D()};b.addEventListener("play",T,false);var ac=function(ai){if(!H("Finish")){if(P.length==1){f("Active playlist only has one clip, onBeforeFinish returned false. Replaying");F()}else{if(q!=(P.length-1)){f("Not the last clip in the playlist, but onBeforeFinish returned false. Returning to the beginning of current clip");b.fp_seek(0)}else{f("Last clip in playlist, but onBeforeFinish returned false, start again from the beginning");b.fp_play(0)}}return M(ai)}K(h);$f.fireEvent(g.id(),"onFinish",q);if(P.length>1&&q<(P.length-1)){f("Not last clip in the playlist, moving to next one");b.fp_play(++q,false,true)}};b.addEventListener("ended",ac,false);var ab=function(ai){K(x,true);$f.fireEvent(g.id(),"onError",q,201);if(z.onFail&&z.onFail instanceof Function){z.onFail.apply(g,[])}};b.addEventListener("error",ab,false);var ae=function(ai){f("got pause event from player"+g.id());if(G){return}if(v==N&&p==y){f("forcing play");setTimeout(function(){b.play()},0);return}if(!H("Pause")){b.fp_resume();return M(ai)}O();K(J);$f.fireEvent(g.id(),"onPause",q)};b.addEventListener("pause",ae,false);var ah=function(ai){$f.fireEvent(g.id(),"onBeforeSeek",q)};b.addEventListener("seeking",ah,false);var Z=function(ai){if(G){G=false;$f.fireEvent(g.id(),"onStop",q)}else{$f.fireEvent(g.id(),"onSeek",q)}f("seek done, currentState",k(v));if(t){t=false;b.fp_play()}else{if(v!=C){b.fp_pause()}}};b.addEventListener("seeked",Z,false);var ad=function(ai){$f.fireEvent(g.id(),"onVolume",b.fp_getVolume())};b.addEventListener("volumechange",ad,false)}function D(){j=setInterval(function(){if(b.fp_getTime()>=b.duration-1){$f.fireEvent(g.id(),"onLastSecond",q);O()}},100)}function O(){clearInterval(j)}function m(){b.fp_play(0)}function u(){}if(s||z.simulateiDevice){if(!window.flashembed.__replaced){var i=window.flashembed;window.flashembed=function(V,aa,W){if(typeof V=="string"){V=document.getElementById(V.replace("#",""))}if(!V){return}var Z=window.getComputedStyle(V,null);var Y=parseInt(Z.width);var T=parseInt(Z.height);while(V.firstChild){V.removeChild(V.firstChild)}var U=document.createElement("div");var X=document.createElement("video");U.appendChild(X);V.appendChild(U);U.style.height=T+"px";U.style.width=Y+"px";U.style.display="block";U.style.position="relative";U.style.background="-webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0.7)))";U.style.cursor="default";U.style.webkitUserDrag="none";X.style.height="100%";X.style.width="100%";X.style.display="block";X.id=aa.id;X.name=aa.id;X.style.cursor="pointer";X.style.webkitUserDrag="none";X.type="video/mp4";X.playerConfig=W.config;$f.fireEvent(W.config.playerId,"onLoad","player")};flashembed.getVersion=i.getVersion;flashembed.asString=i.asString;flashembed.isSupported=function(){return true};flashembed.__replaced=true}var a=g._fireEvent;g._fireEvent=function(T){if(T[0]=="onLoad"&&T[1]=="player"){b=g.getParent().querySelector("video");if(z.controls){b.controls="controls"}o();I();K(x,true);b.fp_setPlaylist(b.playerConfig.playlist);m();a.apply(g,[T])}var U=v!=Q;if(v==Q&&typeof T=="string"){U=true}if(U){return a.apply(g,[T])}};g._swfHeight=function(){return parseInt(b.style.height)};g.hasiPadSupport=function(){return true}}return g});