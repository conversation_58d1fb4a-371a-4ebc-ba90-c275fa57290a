@import url(https://fonts.googleapis.com/css?family=Montserrat:400,700);
@import url(https://fonts.googleapis.com/css?family=Lora:400,700);
@import url(https://fonts.googleapis.com/css?family=Raleway:400,300,700);



body,
input,
textarea,
.mbr-company .list-group-text {
  font-family: 'Raleway', sans-serif;
}
.mbr-footer-content li,
.mbr-footer .mbr-contacts li {
  font-family: 'Raleway', sans-serif;
}
.btn,
.alert,
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
.display-1,
.display-2,
.display-3,
.display-4,
.mbr-figure .mbr-figure-caption,
.mbr-gallery-title,
.mbr-map [data-state-details],
.mbr-price {
  font-family: 'Montserrat', sans-serif;
}
.mbr-footer-content h1,
.mbr-footer .mbr-contacts h1,
.mbr-footer-content h2,
.mbr-footer .mbr-contacts h2,
.mbr-footer-content h3,
.mbr-footer .mbr-contacts h3,
.mbr-footer-content h4,
.mbr-footer .mbr-contacts h4,
.mbr-footer-content p strong,
.mbr-footer .mbr-contacts p strong,
.mbr-footer-content strong,
.mbr-footer .mbr-contacts strong {
  font-family: 'Montserrat', sans-serif;
}
.btn-sm,
.lead a,
.lead blockquote,
.mbr-section-subtitle,
.mbr-section-hero .mbr-section-lead,
.mbr-cards .card-subtitle,
.mbr-testimonial .card-block {
  font-family: 'Lora', serif;
}
.mbr-author-name {
  font-family: 'Montserrat', sans-serif;
}
.mbr-author-desc {
  font-family: 'Lora', serif;
}
.mbr-plan-title {
  font-family: 'Montserrat', sans-serif;
}
.mbr-plan-subtitle,
.mbr-plan-price-desc {
  font-family: 'Lora', serif;
}
.bg-primary {
  background-color: #c0a375 !important;
}
.bg-success {
  background-color: #90a878 !important;
}
.bg-info {
  background-color: #7e9b9f !important;
}
.bg-warning {
  background-color: #f3c649 !important;
}
.bg-danger {
  background-color: #f28281 !important;
}
.btn-primary {
  background-color: #c0a375;
  border-color: #c0a375;
  color: #ffffff;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary.focus,
.btn-primary:active,
.btn-primary.active {
  color: #ffffff;
  background-color: #a07e49;
  border-color: #a07e49;
}
.btn-primary.disabled,
.btn-primary:disabled {
  color: #ffffff !important;
  background-color: #a07e49 !important;
  border-color: #a07e49 !important;
}
.btn-secondary {
  background-color: #bfcecb;
  border-color: #bfcecb;
  color: #ffffff;
}
.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary.focus,
.btn-secondary:active,
.btn-secondary.active {
  color: #ffffff;
  background-color: #94ada8;
  border-color: #94ada8;
}
.btn-secondary.disabled,
.btn-secondary:disabled {
  color: #ffffff !important;
  background-color: #94ada8 !important;
  border-color: #94ada8 !important;
}
.btn-info {
  background-color: #7e9b9f;
  border-color: #7e9b9f;
  color: #ffffff;
}
.btn-info:hover,
.btn-info:focus,
.btn-info.focus,
.btn-info:active,
.btn-info.active {
  color: #ffffff;
  background-color: #597478;
  border-color: #597478;
}
.btn-info.disabled,
.btn-info:disabled {
  color: #ffffff !important;
  background-color: #597478 !important;
  border-color: #597478 !important;
}
.btn-success {
  background-color: #90a878;
  border-color: #90a878;
  color: #ffffff;
}
.btn-success:hover,
.btn-success:focus,
.btn-success.focus,
.btn-success:active,
.btn-success.active {
  color: #ffffff;
  background-color: #6a8153;
  border-color: #6a8153;
}
.btn-success.disabled,
.btn-success:disabled {
  color: #ffffff !important;
  background-color: #6a8153 !important;
  border-color: #6a8153 !important;
}
.btn-warning {
  background-color: #f3c649;
  border-color: #f3c649;
  color: #ffffff;
}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning.focus,
.btn-warning:active,
.btn-warning.active {
  color: #ffffff;
  background-color: #e1a90f;
  border-color: #e1a90f;
}
.btn-warning.disabled,
.btn-warning:disabled {
  color: #ffffff !important;
  background-color: #e1a90f !important;
  border-color: #e1a90f !important;
}
.btn-danger {
  background-color: #f28281;
  border-color: #f28281;
  color: #ffffff;
}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger.focus,
.btn-danger:active,
.btn-danger.active {
  color: #ffffff;
  background-color: #eb3d3c;
  border-color: #eb3d3c;
}
.btn-danger.disabled,
.btn-danger:disabled {
  color: #ffffff !important;
  background-color: #eb3d3c !important;
  border-color: #eb3d3c !important;
}
.btn-primary-outline {
  background: none;
  border-color: #8e7041;
  color: #8e7041;
}
.btn-primary-outline:hover,
.btn-primary-outline:focus,
.btn-primary-outline.focus,
.btn-primary-outline:active,
.btn-primary-outline.active {
  color: #ffffff;
  background-color: #c0a375;
  border-color: #c0a375;
}
.btn-primary-outline.disabled,
.btn-primary-outline:disabled {
  color: #ffffff !important;
  background-color: #c0a375 !important;
  border-color: #c0a375 !important;
}
.btn-secondary-outline {
  background: none;
  border-color: #85a29c;
  color: #85a29c;
}
.btn-secondary-outline:hover,
.btn-secondary-outline:focus,
.btn-secondary-outline.focus,
.btn-secondary-outline:active,
.btn-secondary-outline.active {
  color: #ffffff;
  background-color: #bfcecb;
  border-color: #bfcecb;
}
.btn-secondary-outline.disabled,
.btn-secondary-outline:disabled {
  color: #ffffff !important;
  background-color: #bfcecb !important;
  border-color: #bfcecb !important;
}
.btn-info-outline {
  background: none;
  border-color: #4e6669;
  color: #4e6669;
}
.btn-info-outline:hover,
.btn-info-outline:focus,
.btn-info-outline.focus,
.btn-info-outline:active,
.btn-info-outline.active {
  color: #ffffff;
  background-color: #7e9b9f;
  border-color: #7e9b9f;
}
.btn-info-outline.disabled,
.btn-info-outline:disabled {
  color: #ffffff !important;
  background-color: #7e9b9f !important;
  border-color: #7e9b9f !important;
}
.btn-success-outline {
  background: none;
  border-color: #5d7149;
  color: #5d7149;
}
.btn-success-outline:hover,
.btn-success-outline:focus,
.btn-success-outline.focus,
.btn-success-outline:active,
.btn-success-outline.active {
  color: #ffffff;
  background-color: #90a878;
  border-color: #90a878;
}
.btn-success-outline.disabled,
.btn-success-outline:disabled {
  color: #ffffff !important;
  background-color: #90a878 !important;
  border-color: #90a878 !important;
}
.btn-warning-outline {
  background: none;
  border-color: #c9970d;
  color: #c9970d;
}
.btn-warning-outline:hover,
.btn-warning-outline:focus,
.btn-warning-outline.focus,
.btn-warning-outline:active,
.btn-warning-outline.active {
  color: #ffffff;
  background-color: #f3c649;
  border-color: #f3c649;
}
.btn-warning-outline.disabled,
.btn-warning-outline:disabled {
  color: #ffffff !important;
  background-color: #f3c649 !important;
  border-color: #f3c649 !important;
}
.btn-danger-outline {
  background: none;
  border-color: #e82625;
  color: #e82625;
}
.btn-danger-outline:hover,
.btn-danger-outline:focus,
.btn-danger-outline.focus,
.btn-danger-outline:active,
.btn-danger-outline.active {
  color: #ffffff;
  background-color: #f28281;
  border-color: #f28281;
}
.btn-danger-outline.disabled,
.btn-danger-outline:disabled {
  color: #ffffff !important;
  background-color: #f28281 !important;
  border-color: #f28281 !important;
}
.text-primary {
  color: #c0a375 !important;
}
.text-success {
  color: #90a878 !important;
}
.text-info {
  color: #7e9b9f !important;
}
.text-warning {
  color: #f3c649 !important;
}
.text-danger {
  color: #f28281 !important;
}
.alert-success {
  background-color: #90a878;
}
.alert-info {
  background-color: #7e9b9f;
}
.alert-warning {
  background-color: #f3c649;
}
.alert-danger {
  background-color: #f28281;
}
.btn-social {
  border-color: #c0a375;
}
.btn-social:hover {
  background: #c0a375;
}
.mbr-company .list-group-item.active .list-group-text {
  color: #c0a375;
}
.mbr-footer p a,
.mbr-footer ul a {
  color: #c0a375;
}
.mbr-footer-content li::before,
.mbr-footer .mbr-contacts li::before {
  background: #c0a375;
}
.mbr-footer-content li a:hover,
.mbr-footer .mbr-contacts li a:hover {
  color: #c0a375;
}
.lead a,
.lead a:hover {
  color: #c0a375;
}
.lead blockquote {
  border-color: #c0a375;
}
.mbr-plan-header.bg-primary .mbr-plan-subtitle,
.mbr-plan-header.bg-primary .mbr-plan-price-desc {
  color: #e8ddcd;
}
.mbr-plan-header.bg-success .mbr-plan-subtitle,
.mbr-plan-header.bg-success .mbr-plan-price-desc {
  color: #d0dac6;
}
.mbr-plan-header.bg-info .mbr-plan-subtitle,
.mbr-plan-header.bg-info .mbr-plan-price-desc {
  color: #c7d4d5;
}
.mbr-plan-header.bg-warning .mbr-plan-subtitle,
.mbr-plan-header.bg-warning .mbr-plan-price-desc {
  color: #ffffff;
}
.mbr-plan-header.bg-danger .mbr-plan-subtitle,
.mbr-plan-header.bg-danger .mbr-plan-price-desc {
  color: #ffffff;
}
.mbr-small-footer a,
.mbr-gallery-filter li:hover {
  color: #c0a375;
}
.scrollToTop_wraper {
  opacity: 0 !important;
}
#menu-0 .hide-buttons .nav-btn {
  display: none !important;
}
#menu-0 .navbar-caption {
  color: #ffffff;
}
#menu-0 .navbar-toggler {
  color: #ffffff;
}
#menu-0 .link,
#menu-0 .dropdown-item {
  color: #ffffff;
}
#menu-0 .link {
  font-size: 0.75rem;
}
#menu-0 .dropdown-item,
#menu-0 .nav-dropdown-sm .link {
  font-size: 0.812rem;
}
#menu-0 .link:hover,
#menu-0 .dropdown-item:hover,
#menu-0 .link:focus,
#menu-0 .dropdown-item:focus {
  color: #c0a375;
}
#menu-0 .link[aria-expanded="true"],
#menu-0 .dropdown-menu {
  background: #0e0e0e;
}
#menu-0 .nav-dropdown-sm .link:focus,
#menu-0 .nav-dropdown-sm .link:hover,
#menu-0 .nav-dropdown-sm .dropdown-item:focus,
#menu-0 .nav-dropdown-sm .dropdown-item:hover {
  background: #202020!important;
}
#menu-0 .navbar,
#menu-0 .nav-dropdown-sm,
#menu-0 .nav-dropdown-sm .link[aria-expanded="true"],
#menu-0 .nav-dropdown-sm .dropdown-menu {
  background: #282828;
}
#menu-0 .bg-color.transparent .link {
  color: #ffffff;
  transition: none;
}
#menu-0 .bg-color.transparent.opened .link {
  transition: color 0.2s ease-in-out;
}
#menu-0 .bg-color.transparent.opened .link:hover,
#menu-0 .bg-color.transparent.opened .link:focus {
  color: #c0a375;
}
#menu-0 .link[aria-expanded="true"],
#menu-0 .dropdown-item[aria-expanded="true"] {
  color: #c0a375!important;
}
#menu-0.mbr-navbar--stuck .mbr-navbar__section {
  background: #2c2c2c;
}
#menu-0 .mbr-navbar__hamburger {
  color: #ffffff;
}
#menu-0 .close-icon::before,
#menu-0 .close-icon::after {
  background-color: #ffffff;
}
