/*!
 * Default theme v2 (https://mobirise.com/)
 * Copyright 2016 Mobirise
 */
.container {
  position: relative; }

.btn {
  margin-bottom: 0.5rem; }
  .btn + .btn {
    margin-left: 1rem; }
  @media (max-width: 767px) {
    .btn {
      white-space: normal; }
      .btn + .btn {
        margin-left: 0; } }

.bg-primary {
  background-color: #c0a375 !important; }

.bg-success {
  background-color: #90a878 !important; }

.bg-info {
  background-color: #7e9b9f !important; }

.bg-warning {
  background-color: #f3c649 !important; }

.bg-danger {
  background-color: #f28281 !important; }

.bg-none {
  background: none !important; }

.btn {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  letter-spacing: 2px;
  padding: 0.75rem 2.1875rem;
  font-size: 0.75rem;
  line-height: 1.5;
  border-radius: 3px;
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out; }

.btn-sm {
  padding: 0.625rem 2.1874rem;
  font-size: 0.9687rem;
  line-height: 1.5;
  border-radius: 3px;
  font-family: "Lora";
  font-style: italic;
  font-weight: 400;
  letter-spacing: 0px; }

.btn-lg {
  padding: 0.75rem 2.1874rem;
  font-size: 0.9687rem;
  line-height: 1.33333;
  border-radius: 3px; }

.btn-xlg {
  padding: 0.75rem 2.1874rem;
  font-size: 1.1875rem;
  line-height: 1.33333;
  border-radius: 3px; }

.btn-primary {
  background-color: #c0a375;
  border-color: #c0a375;
  box-shadow: none;
  color: #fff; }
  .btn-primary:hover {
    color: #fff;
    background-color: #a07e49;
    border-color: #a07e49;
    box-shadow: none; }
  .btn-primary:focus, .btn-primary.focus {
    color: #fff;
    background-color: #a07e49;
    border-color: #a07e49; }
  .btn-primary:active, .btn-primary.active {
    color: #fff;
    background-color: #a07e49;
    border-color: #a07e49;
    background-image: none;
    box-shadow: none; }
  .btn-primary.disabled, .btn-primary:disabled {
    color: #fff !important;
    background-color: #a07e49 !important;
    border-color: #a07e49 !important;
    opacity: 0.85; }

.btn-secondary {
  background-color: #bfcecb;
  border-color: #bfcecb;
  box-shadow: none;
  color: #fff; }
  .btn-secondary:hover {
    color: #fff;
    background-color: #94ada8;
    border-color: #94ada8;
    box-shadow: none; }
  .btn-secondary:focus, .btn-secondary.focus {
    color: #fff;
    background-color: #94ada8;
    border-color: #94ada8; }
  .btn-secondary:active, .btn-secondary.active {
    color: #fff;
    background-color: #94ada8;
    border-color: #94ada8;
    background-image: none;
    box-shadow: none; }
  .btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff !important;
    background-color: #94ada8 !important;
    border-color: #94ada8 !important;
    opacity: 0.85; }

.btn-info {
  background-color: #7e9b9f;
  border-color: #7e9b9f;
  box-shadow: none;
  color: #fff; }
  .btn-info:hover {
    color: #fff;
    background-color: #597478;
    border-color: #597478;
    box-shadow: none; }
  .btn-info:focus, .btn-info.focus {
    color: #fff;
    background-color: #597478;
    border-color: #597478; }
  .btn-info:active, .btn-info.active {
    color: #fff;
    background-color: #597478;
    border-color: #597478;
    background-image: none;
    box-shadow: none; }
  .btn-info.disabled, .btn-info:disabled {
    color: #fff !important;
    background-color: #597478 !important;
    border-color: #597478 !important;
    opacity: 0.85; }

.btn-success {
  background-color: #90a878;
  border-color: #90a878;
  box-shadow: none;
  color: #fff; }
  .btn-success:hover {
    color: #fff;
    background-color: #6a8153;
    border-color: #6a8153;
    box-shadow: none; }
  .btn-success:focus, .btn-success.focus {
    color: #fff;
    background-color: #6a8153;
    border-color: #6a8153; }
  .btn-success:active, .btn-success.active {
    color: #fff;
    background-color: #6a8153;
    border-color: #6a8153;
    background-image: none;
    box-shadow: none; }
  .btn-success.disabled, .btn-success:disabled {
    color: #fff !important;
    background-color: #6a8153 !important;
    border-color: #6a8153 !important;
    opacity: 0.85; }

.btn-warning {
  background-color: #f3c649;
  border-color: #f3c649;
  box-shadow: none;
  color: #fff; }
  .btn-warning:hover {
    color: #fff;
    background-color: #e1a90f;
    border-color: #e1a90f;
    box-shadow: none; }
  .btn-warning:focus, .btn-warning.focus {
    color: #fff;
    background-color: #e1a90f;
    border-color: #e1a90f; }
  .btn-warning:active, .btn-warning.active {
    color: #fff;
    background-color: #e1a90f;
    border-color: #e1a90f;
    background-image: none;
    box-shadow: none; }
  .btn-warning.disabled, .btn-warning:disabled {
    color: #fff !important;
    background-color: #e1a90f !important;
    border-color: #e1a90f !important;
    opacity: 0.85; }

.btn-danger {
  background-color: #f28281;
  border-color: #f28281;
  box-shadow: none;
  color: #fff; }
  .btn-danger:hover {
    color: #fff;
    background-color: #eb3d3c;
    border-color: #eb3d3c;
    box-shadow: none; }
  .btn-danger:focus, .btn-danger.focus {
    color: #fff;
    background-color: #eb3d3c;
    border-color: #eb3d3c; }
  .btn-danger:active, .btn-danger.active {
    color: #fff;
    background-color: #eb3d3c;
    border-color: #eb3d3c;
    background-image: none;
    box-shadow: none; }
  .btn-danger.disabled, .btn-danger:disabled {
    color: #fff !important;
    background-color: #eb3d3c !important;
    border-color: #eb3d3c !important;
    opacity: 0.85; }

.btn-white {
  background-color: #fff;
  border-color: #fff;
  box-shadow: none;
  color: #535353; }
  .btn-white:hover {
    color: #fff;
    background-color: #d4d4d4;
    border-color: #d4d4d4;
    box-shadow: none; }
  .btn-white:focus, .btn-white.focus {
    color: #fff;
    background-color: #d4d4d4;
    border-color: #d4d4d4; }
  .btn-white:active, .btn-white.active {
    color: #fff;
    background-color: #d4d4d4;
    border-color: #d4d4d4;
    background-image: none;
    box-shadow: none; }
  .btn-white.disabled, .btn-white:disabled {
    color: #fff !important;
    background-color: #d4d4d4 !important;
    border-color: #d4d4d4 !important;
    opacity: 0.85; }

.btn-black {
  background-color: #535353;
  border-color: #535353;
  box-shadow: none;
  color: #fff; }
  .btn-black:hover {
    color: #fff;
    background-color: #2d2d2d;
    border-color: #2d2d2d;
    box-shadow: none; }
  .btn-black:focus, .btn-black.focus {
    color: #fff;
    background-color: #2d2d2d;
    border-color: #2d2d2d; }
  .btn-black:active, .btn-black.active {
    color: #fff;
    background-color: #2d2d2d;
    border-color: #2d2d2d;
    background-image: none;
    box-shadow: none; }
  .btn-black.disabled, .btn-black:disabled {
    color: #fff !important;
    background-color: #2d2d2d !important;
    border-color: #2d2d2d !important;
    opacity: 0.85; }

.btn-primary-outline {
  background: none;
  border-color: #8e7041;
  color: #8e7041; }
  .btn-primary-outline:focus, .btn-primary-outline.focus, .btn-primary-outline:active, .btn-primary-outline.active {
    color: #fff;
    background-color: #c0a375;
    border-color: #c0a375; }
  .btn-primary-outline:hover {
    color: #fff;
    background-color: #c0a375;
    border-color: #c0a375; }
  .btn-primary-outline.disabled, .btn-primary-outline:disabled {
    color: #fff !important;
    background-color: #c0a375 !important;
    border-color: #c0a375 !important;
    opacity: 0.85; }

.btn-secondary-outline {
  background: none;
  border-color: #85a29c;
  color: #85a29c; }
  .btn-secondary-outline:focus, .btn-secondary-outline.focus, .btn-secondary-outline:active, .btn-secondary-outline.active {
    color: #fff;
    background-color: #bfcecb;
    border-color: #bfcecb; }
  .btn-secondary-outline:hover {
    color: #fff;
    background-color: #bfcecb;
    border-color: #bfcecb; }
  .btn-secondary-outline.disabled, .btn-secondary-outline:disabled {
    color: #fff !important;
    background-color: #bfcecb !important;
    border-color: #bfcecb !important;
    opacity: 0.85; }

.btn-info-outline {
  background: none;
  border-color: #4e6669;
  color: #4e6669; }
  .btn-info-outline:focus, .btn-info-outline.focus, .btn-info-outline:active, .btn-info-outline.active {
    color: #fff;
    background-color: #7e9b9f;
    border-color: #7e9b9f; }
  .btn-info-outline:hover {
    color: #fff;
    background-color: #7e9b9f;
    border-color: #7e9b9f; }
  .btn-info-outline.disabled, .btn-info-outline:disabled {
    color: #fff !important;
    background-color: #7e9b9f !important;
    border-color: #7e9b9f !important;
    opacity: 0.85; }

.btn-success-outline {
  background: none;
  border-color: #5d7149;
  color: #5d7149; }
  .btn-success-outline:focus, .btn-success-outline.focus, .btn-success-outline:active, .btn-success-outline.active {
    color: #fff;
    background-color: #90a878;
    border-color: #90a878; }
  .btn-success-outline:hover {
    color: #fff;
    background-color: #90a878;
    border-color: #90a878; }
  .btn-success-outline.disabled, .btn-success-outline:disabled {
    color: #fff !important;
    background-color: #90a878 !important;
    border-color: #90a878 !important;
    opacity: 0.85; }

.btn-warning-outline {
  background: none;
  border-color: #c9970d;
  color: #c9970d; }
  .btn-warning-outline:focus, .btn-warning-outline.focus, .btn-warning-outline:active, .btn-warning-outline.active {
    color: #fff;
    background-color: #f3c649;
    border-color: #f3c649; }
  .btn-warning-outline:hover {
    color: #fff;
    background-color: #f3c649;
    border-color: #f3c649; }
  .btn-warning-outline.disabled, .btn-warning-outline:disabled {
    color: #fff !important;
    background-color: #f3c649 !important;
    border-color: #f3c649 !important;
    opacity: 0.85; }

.btn-danger-outline {
  background: none;
  border-color: #e82625;
  color: #e82625; }
  .btn-danger-outline:focus, .btn-danger-outline.focus, .btn-danger-outline:active, .btn-danger-outline.active {
    color: #fff;
    background-color: #f28281;
    border-color: #f28281; }
  .btn-danger-outline:hover {
    color: #fff;
    background-color: #f28281;
    border-color: #f28281; }
  .btn-danger-outline.disabled, .btn-danger-outline:disabled {
    color: #fff !important;
    background-color: #f28281 !important;
    border-color: #f28281 !important;
    opacity: 0.85; }

.btn-white-outline {
  background: none;
  border-color: #fff;
  color: #fff; }
  .btn-white-outline:focus, .btn-white-outline.focus, .btn-white-outline:active, .btn-white-outline.active {
    color: #535353;
    background-color: #fff;
    border-color: #fff; }
  .btn-white-outline:hover {
    color: #535353;
    background-color: #fff;
    border-color: #fff; }
  .btn-white-outline.disabled, .btn-white-outline:disabled {
    color: #535353 !important;
    background-color: #fff !important;
    border-color: #fff !important;
    opacity: 0.85; }

.btn-black-outline {
  background: none;
  border-color: #202020;
  color: #202020; }
  .btn-black-outline:focus, .btn-black-outline.focus, .btn-black-outline:active, .btn-black-outline.active {
    color: #fff;
    background-color: #535353;
    border-color: #535353; }
  .btn-black-outline:hover {
    color: #fff;
    background-color: #535353;
    border-color: #535353; }
  .btn-black-outline.disabled, .btn-black-outline:disabled {
    color: #fff !important;
    background-color: #535353 !important;
    border-color: #535353 !important;
    opacity: 0.85; }

a.mbr-iconfont:hover {
  text-decoration: none; }

.btn-social {
  font-size: 20px;
  border-radius: 50%;
  padding: 0;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  position: relative;
  border: 2px solid #c0a375;
  color: #232323; }
  .btn-social i {
    top: 0;
    line-height: 44px;
    width: 44px; }
  .btn-social:hover {
    color: #fff;
    background: #c0a375; }
  .btn-social + .btn {
    margin-left: 0.1rem; }

p.lead,
.lead p {
  font-size: 1.07rem;
  font-weight: 300;
  margin-bottom: 2.3125rem; }

.article .lead p, .article .lead ul, .article .lead ol, .article .lead pre, .article .lead blockquote {
  margin-bottom: 0; }

.lead {
  font-size: 1.07rem;
  font-weight: 300; }
  .lead a {
    font-family: "Lora";
    font-style: italic;
    font-weight: 400; }
    .lead a, .lead a:hover {
      color: #c0a375;
      text-decoration: none; }
  .lead h1 {
    font-size: 3rem;
    font-weight: 600;
    letter-spacing: -1px;
    margin-bottom: 1.605rem; }
  .lead h2 {
    font-size: 2.5rem;
    font-weight: 600;
    letter-spacing: -1px;
    margin-bottom: 1.605rem; }
  .lead h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.605rem; }
  .lead h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.605rem; }
  .lead blockquote {
    font-family: "Lora";
    font-style: italic;
    padding: 10px 0 10px 20px;
    border-left: 4px solid #c39f76;
    font-size: 1.09rem;
    position: relative; }
  .lead ul, .lead ol, .lead pre, .lead blockquote {
    margin-bottom: 2.3125rem; }
  .lead pre {
    background: #f4f4f4;
    padding: 10px 24px;
    white-space: pre-wrap; }

.display-1 {
  font-size: 4.39rem;
  font-weight: 600;
  letter-spacing: -2px;
  margin-bottom: 0.2843em;
  margin-top: 3.9rem; }

.display-2 {
  font-size: 3rem;
  font-weight: 600;
  letter-spacing: -1px;
  margin-bottom: 1.5625rem;
  margin-top: 3.5rem; }

.display-3 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-top: 3.2rem;
  margin-bottom: 2rem; }

.display-4 {
  font-size: 2rem;
  font-weight: 600;
  margin-top: 2.3rem;
  margin-bottom: 1.8rem; }

.modal-backdrop {
  background: -webkit-linear-gradient(left, #564740, #3a414a);
  background: -moz-linear-gradient(left, #564740, #3a414a);
  background: -o-linear-gradient(left, #564740, #3a414a);
  background: -ms-linear-gradient(left, #564740, #3a414a);
  background: linear-gradient(left, #564740, #3a414a);
  opacity: 0.94; }

.form-control {
  background-color: #f5f5f5;
  border-radius: 3px;
  box-shadow: none;
  color: #565656;
  font-size: 0.875rem;
  line-height: 1.43;
  min-height: 3.5em;
  padding: 0.5em 1.07em 0.5em; }
  .form-control, .form-control:focus {
    border: 1px solid #e8e8e8; }
  .form-active .form-control:invalid {
    border-color: #f28281; }

.form-control-label {
  cursor: pointer;
  font-size: 0.875rem;
  margin-bottom: 0.357em;
  padding: 0; }

.alert {
  border-radius: 0;
  border: 0;
  font-family: 'Montserrat', sans-serif;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1.875rem;
  padding: 1.25rem;
  position: relative; }
  .alert.alert-form:after {
    background-color: inherit;
    bottom: -7px;
    content: "";
    display: block;
    height: 14px;
    left: 50%;
    margin-left: -7px;
    position: absolute;
    transform: rotate(45deg);
    width: 14px; }

.alert-success {
  background-color: #90a878;
  color: #fff; }

.alert-info {
  background-color: #7e9b9f;
  color: #fff; }

.alert-warning {
  background-color: #f3c649;
  color: #fff; }

.alert-danger {
  background-color: #f28281;
  color: #fff; }

/**
 * TYPOGRAPHY
 */
body {
  font-family: 'Raleway', sans-serif;
  color: #232323; }

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6,
.display-1, .display-2, .display-3, .display-4 {
  font-family: 'Montserrat', sans-serif;
  word-break: break-word;
  word-wrap: break-word; }

input, textarea {
  font-family: "Raleway"; }

input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active {
  transition-delay: 9999s;
  transition-property: background-color, color; }

.text-primary {
  color: #c0a375 !important; }

.text-success {
  color: #90a878 !important; }

.text-info {
  color: #7e9b9f !important; }

.text-warning {
  color: #f3c649 !important; }

.text-danger {
  color: #f28281 !important; }

.text-white {
  color: #fff !important; }

.text-black {
  color: #535353 !important; }

.mbr-section {
  position: relative;
  padding-top: 120px;
  padding-bottom: 120px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover; }

.mbr-section-full {
  display: table;
  height: 100vh;
  padding-bottom: 0;
  padding-top: 0;
  table-layout: fixed;
  width: 100%; }
  .mbr-section-full > .mbr-table-cell {
    width: 100%; }

.mbr-section-small {
  padding-top: 60px;
  padding-bottom: 60px; }

.mbr-section-title {
  margin-top: 0; }

.mbr-section__container--first {
  padding-top: 120px;
  padding-bottom: 40px; }

.mbr-section__container--middle {
  padding-top: 0;
  padding-bottom: 40px; }

.mbr-section__container--last {
  padding-top: 0;
  padding-bottom: 120px; }

.mbr-section-sm-padding {
  padding-top: 40px;
  padding-bottom: 40px; }

.mbr-section-md-padding {
  padding-top: 90px;
  padding-bottom: 90px; }

.mbr-section-nopadding {
  padding-top: 0;
  padding-bottom: 0; }

.mbr-section-subtitle {
  display: block;
  font-style: italic;
  font-weight: 400;
  font-size: 17px;
  font-family: "Lora";
  line-height: 26px;
  color: #8c8c8c;
  margin-bottom: 30px; }

.mbr-section-title + .mbr-section-subtitle {
  margin-top: -21px; }

.mbr-section-hero::before {
  display: none !important; }
.mbr-section-hero .mbr-section-title {
  color: #fff;
  margin-bottom: 1.6875rem; }
.mbr-section-hero .mbr-section-lead {
  color: #fff;
  font-family: Lora;
  font-size: 1.5rem;
  font-style: italic;
  margin-top: -1em; }
.mbr-section-hero .mbr-section-text {
  color: #fff; }

@media (max-width: 767px) {
  .mbr-section {
    padding-top: 60px;
    padding-bottom: 60px; }

  .mbr-section-full {
    padding-bottom: 0;
    padding-top: 0; }

  .mbr-section__container--first {
    padding-top: 60px;
    padding-bottom: 40px; }

  .mbr-section__container--middle {
    padding-top: 0;
    padding-bottom: 40px; }

  .mbr-section__container--last {
    padding-top: 0;
    padding-bottom: 60px; }

  .mbr-section-sm-padding {
    padding-top: 40px;
    padding-bottom: 40px; }

  .mbr-section-md-padding {
    padding-top: 60px;
    padding-bottom: 60px; }

  .mbr-section-nopadding {
    padding-top: 0;
    padding-bottom: 0; }

  .mbr-section-subtitle {
    margin-bottom: 0; }

  .mbr-section-hero.mbr-after-navbar .mbr-section {
    padding-top: 120px; }
  .mbr-section-hero.mbr-section-with-arrow .mbr-section {
    padding-bottom: 120px; }
  .mbr-section-hero .mbr-section-title {
    font-size: 3.125rem !important; }
  .mbr-section-hero .mbr-section-lead {
    font-size: 1.125rem !important; }
  .mbr-section-hero .mbr-section-btn .btn {
    font-size: 0.75rem !important;
    line-height: 1.5;
    padding: 0.75rem 2.1875rem; } }
.mbr-section-full .mbr-overlay {
  min-height: 100vh; }

.mbr-overlay {
  background-color: #000;
  bottom: 0;
  left: 0;
  opacity: 0.5;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1; }
  .mbr-overlay ~ * {
    z-index: 2; }

.mbr-section-full .mbr-background-video,
.mbr-section-full .mbr-background-video-preview {
  min-height: 100vh; }

.mbr-background-video,
.mbr-background-video-preview {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0; }

.mbr-background-video-preview {
  z-index: 0 !important; }

.mbr-table {
  display: table;
  width: 100%; }

.mbr-table-full {
  height: 100%;
  position: relative;
  width: 100%; }

.mbr-table-cell {
  display: table-cell;
  float: none;
  padding-bottom: 0;
  padding-top: 0;
  position: relative;
  vertical-align: middle; }

.mbr-table-md-up {
  width: 100%; }
  .mbr-table-md-up .mbr-table-cell {
    display: block; }
  .mbr-table-md-up .mbr-table-cell + .mbr-table-cell {
    padding-top: 40px; }
  @media (min-width: 768px) {
    .mbr-table-md-up {
      display: table; }
      .mbr-table-md-up .mbr-table-cell {
        display: table-cell; }
      .mbr-table-md-up .mbr-table-cell + .mbr-table-cell {
        padding-top: 0; } }

.mbr-figure {
  display: block;
  margin: 0;
  overflow: hidden;
  position: relative;
  width: 100%; }
  .mbr-figure img, .mbr-figure iframe {
    display: block;
    width: 100%; }
  .mbr-figure .mbr-figure-caption {
    background: #2e2e2e;
    color: #fff;
    font-family: Montserrat;
    padding: 1.5rem 0;
    text-align: center;
    width: 100%; }
  .mbr-figure .mbr-figure-caption-over {
    background: -moz-linear-gradient(left, rgba(67, 76, 99, 0.85), rgba(188, 155, 114, 0.85)) !important;
    background: -ms-linear-gradient(left, rgba(67, 76, 99, 0.85), rgba(188, 155, 114, 0.85)) !important;
    background: -o-linear-gradient(left, rgba(67, 76, 99, 0.85), rgba(188, 155, 114, 0.85)) !important;
    background: -webkit-linear-gradient(left, rgba(67, 76, 99, 0.85), rgba(188, 155, 114, 0.85)) !important;
    background: linear-gradient(left, rgba(67, 76, 99, 0.85), rgba(188, 155, 114, 0.85)) !important;
    bottom: 0;
    position: absolute; }

.mbr-map {
  height: 25rem;
  position: relative; }
  .mbr-map iframe {
    height: 100%;
    width: 100%; }
  .mbr-map [data-state-details] {
    color: #6b6763;
    font-family: Montserrat;
    height: 1.5em;
    margin-top: -0.75em;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    position: absolute;
    text-align: center;
    top: 50%;
    width: 100%; }
  .mbr-map[data-state] {
    background: #e9e5dc; }
  .mbr-map[data-state="loading"] [data-state-details] {
    display: none; }
  .mbr-map[data-state="loading"]::after {
    content: "";
    -webkit-animation: btnCircleLoading .6s infinite linear;
    animation: btnCircleLoading .6s infinite linear;
    border-radius: 50%;
    border: 6px rgba(255, 255, 255, 0.35) solid;
    border-top-color: #fff;
    height: 40px;
    left: 50%;
    margin-left: -20px;
    margin-top: -20px;
    position: absolute;
    top: 50%;
    width: 40px; }

/*-------

   Gallery

-------*/
.mbr-gallery .mbr-gallery-item {
  position: relative;
  display: inline-block;
  width: 25%; }
  @media (max-width: 768px) {
    .mbr-gallery .mbr-gallery-item {
      width: 50%; } }
  @media (max-width: 400px) {
    .mbr-gallery .mbr-gallery-item {
      width: 100%; } }

.mbr-gallery .mbr-gallery-item img {
  width: 100%;
  opacity: 1;
  -webkit-transition: .2s opacity ease-in-out;
  transition: .2s opacity ease-in-out; }

.mbr-gallery .mbr-gallery-item > a:hover img {
  opacity: 1; }

.mbr-gallery .mbr-gallery-item > a {
  background: #fff;
  display: block;
  outline: none;
  position: relative; }
  .mbr-gallery .mbr-gallery-item > a::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: -webkit-linear-gradient(left, #554346, #45505b) !important;
    background: -moz-linear-gradient(left, #554346, #45505b) !important;
    background: -o-linear-gradient(left, #554346, #45505b) !important;
    background: -ms-linear-gradient(left, #554346, #45505b) !important;
    background: linear-gradient(left, #554346, #45505b) !important;
    opacity: 0;
    -webkit-transition: .2s opacity ease-in-out;
    transition: .2s opacity ease-in-out; }

.mbr-gallery .mbr-gallery-item .icon {
  -webkit-transform: translateX(-50%) translateY(-50%);
  -webkit-transition: .2s opacity ease-in-out;
  color: #000;
  font-size: 30px;
  height: 69px;
  left: 50%;
  opacity: 0;
  position: absolute;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  transition: .2s opacity ease-in-out;
  width: 69px; }
  .mbr-gallery .mbr-gallery-item .icon::before, .mbr-gallery .mbr-gallery-item .icon::after {
    content: "";
    display: block;
    position: absolute;
    height: 69px;
    width: 1px;
    margin-left: 34.5px;
    background-color: #fff; }
  .mbr-gallery .mbr-gallery-item .icon::after {
    width: 69px;
    height: 1px;
    margin-left: 0;
    margin-top: 34.5px; }

.mbr-gallery .mbr-gallery-item > a:hover .icon {
  opacity: 1; }

.mbr-gallery .mbr-gallery-item > a:hover::before {
  opacity: 0.9; }

.mbr-gallery-title {
  font-family: Montserrat;
  font-size: 0.9em;
  position: absolute;
  display: block;
  width: 100%;
  bottom: 0;
  padding: 1rem;
  color: #fff;
  background: -webkit-linear-gradient(left, rgba(85, 67, 70, 0.85), rgba(69, 80, 91, 0.85)) !important;
  background: -moz-linear-gradient(left, rgba(85, 67, 70, 0.85), rgba(69, 80, 91, 0.85)) !important;
  background: -o-linear-gradient(left, rgba(85, 67, 70, 0.85), rgba(69, 80, 91, 0.85)) !important;
  background: -ms-linear-gradient(left, rgba(85, 67, 70, 0.85), rgba(69, 80, 91, 0.85)) !important;
  background: linear-gradient(left, rgba(85, 67, 70, 0.85), rgba(69, 80, 91, 0.85)) !important;
  -webkit-transition: .2s background ease-in-out;
  transition: .2s background ease-in-out; }

.mbr-gallery .mbr-gallery-item > a:hover .mbr-gallery-title {
  background: transparent !important; }

/* remove spacing */
.mbr-gallery .mbr-gallery-row.no-gutter {
  margin: 0; }

.mbr-gallery .mbr-gallery-row.no-gutter .mbr-gallery-item {
  padding: 0; }

/* container */
.mbr-gallery .container.mbr-gallery-layout-default {
  padding: 93px 0; }

/* fix horizontal scrollbar */
.mbr-gallery .mbr-gallery-layout-default,
.mbr-gallery .mbr-gallery-layout-article {
  overflow: hidden; }

/* lightbox */
.mbr-gallery .modal {
  position: fixed;
  overflow: hidden;
  padding-right: 0 !important; }

.mbr-gallery .modal-content {
  border-radius: 0; }

.mbr-gallery .modal-body {
  padding: 0; }

.mbr-gallery .modal-body img {
  width: 100%; }

.mbr-gallery .modal .close {
  position: fixed;
  background: #1b1b1b;
  opacity: 0.5;
  font-size: 55px;
  font-weight: 300;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  color: #fff;
  top: 2.5rem;
  right: 2.5rem;
  line-height: 61px;
  border: 0;
  text-align: center;
  text-shadow: none;
  z-index: 5;
  -webkit-transition: opacity .3s ease;
  -moz-transition: opacity .3s ease;
  -o-transition: opacity .3s ease;
  transition: opacity .3s ease; }

.mbr-gallery .modal .close:hover {
  opacity: 1;
  background: #000;
  color: #fff; }

.mbr-gallery .modal.in .modal-dialog {
  margin: 0 auto; }

/* modal back color opacity */
.modal-backdrop.in {
  opacity: 0.8;
  filter: alpha(opacity=80); }

@media (max-width: 768px) {
  .mbr-gallery .carousel-indicators,
  .mbr-gallery .carousel-control,
  .mbr-gallery .modal .close {
    position: fixed; } }
/* fix fade in effect */
.mbr-gallery .modal.fade .modal-dialog {
  -webkit-transition: margin-top 0.3s ease-out;
  -moz-transition: margin-top 0.3s ease-out;
  -o-transition: margin-top 0.3s ease-out;
  transition: margin-top 0.3s ease-out; }

.mbr-gallery .modal.in .modal-dialog,
.mbr-gallery .modal.fade .modal-dialog {
  -webkit-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none; }

/*-------

   Slider

-------*/
.mbr-slider .carousel-inner > .active,
.mbr-slider .carousel-inner > .next,
.mbr-slider .carousel-inner > .prev {
  display: table; }

.mbr-slider .carousel-control {
  position: absolute;
  width: 70px;
  height: 70px;
  top: 50%;
  margin-top: -35px;
  line-height: 70px;
  border-radius: 50%;
  color: inherit;
  background: #1b1b1b;
  border: 0;
  opacity: 0.5;
  text-shadow: none;
  z-index: 5;
  color: #fff;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s; }

.mbr-gallery .mbr-slider .carousel-control {
  position: fixed; }
  @media (max-width: 991px) {
    .mbr-gallery .mbr-slider .carousel-control {
      bottom: 2.5rem;
      margin-top: 0;
      top: auto;
      z-index: 17; } }

.mbr-gallery .mbr-slider .carousel-inner > .active {
  display: block; }

.mbr-slider .carousel-control.left {
  left: 0;
  margin-left: 2.5rem; }

.mbr-slider .carousel-control.right {
  right: 0;
  margin-right: 2.5rem; }

.mbr-slider .carousel-control .icon-next,
.mbr-slider .carousel-control .icon-prev {
  margin-top: -18px;
  font-size: 40px;
  line-height: 27px; }

.mbr-slider .carousel-control:hover {
  background: #1b1b1b;
  color: #fff;
  opacity: 1; }

.mbr-slider .carousel-indicators {
  position: absolute;
  bottom: 0;
  margin-bottom: 1.5rem !important; }
  @media (max-width: 543px) {
    .mbr-slider .carousel-indicators {
      display: none; } }

.mbr-gallery .mbr-slider .carousel-indicators {
  position: fixed;
  margin-bottom: 2.5rem !important; }
  @media (max-width: 991px) {
    .mbr-gallery .mbr-slider .carousel-indicators {
      margin-bottom: 3.625rem !important;
      padding-left: 2.5rem;
      padding-right: 2.5rem; } }

.mbr-slider .carousel-indicators li,
.mbr-slider .carousel-indicators .active {
  width: 15px;
  height: 15px;
  margin: 3px;
  background: #1b1b1b;
  border: 0;
  opacity: 0.5; }

.mbr-slider .carousel-indicators .active {
  border: 4px solid #1b1b1b;
  background: #fff; }

@media (max-width: 767px) {
  .mbr-slider .carousel-control {
    top: auto;
    bottom: 20px; }

  .mbr-slider > .container .carousel-control {
    margin-bottom: 0px; } }
/* boxed slider */
.mbr-slider > .boxed-slider {
  position: relative;
  padding: 93px 0; }

.mbr-slider > .boxed-slider > div {
  position: relative; }

.mbr-slider > .container img {
  width: 100%; }

.mbr-slider > .container img + .row {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 2; }

.mbr-slider .mbr-section {
  padding: 0;
  background-attachment: scroll; }

.mbr-slider .mbr-table-cell {
  padding: 0; }

.mbr-slider > .container .carousel-indicators {
  margin-bottom: 3px; }

/* article slider */
.mbr-slider > .article-slider .mbr-section,
.mbr-slider > .article-slider .mbr-section .mbr-table-cell {
  padding-top: 0;
  padding-bottom: 0; }

/*-------

   Cards

-------*/
.mbr-cards-row {
  position: relative;
  margin-left: 0 !important;
  margin-right: 0 !important; }
.mbr-cards-col {
  padding-left: 0;
  padding-right: 0;
  padding-top: 120px;
  padding-bottom: 120px; }
  @media (max-width: 767px) {
    .mbr-cards-col {
      padding-top: 60px;
      padding-bottom: 60px; } }
.mbr-cards .striped .card {
  padding-left: 10%;
  padding-right: 10%; }
.mbr-cards .striped .mbr-cards-col:nth-child(2n+1) {
  background: #2e2e2e;
  color: #fff; }
  .mbr-cards .striped .mbr-cards-col:nth-child(2n+1) .card-subtitle {
    color: #979797; }
.mbr-cards .card {
  text-align: center;
  border: 0;
  background: none;
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0; }
  @media (max-width: 991px) {
    .mbr-cards .card {
      padding-left: 0;
      padding-right: 0; } }
@media (min-width: 992px) {
  .mbr-cards-row {
    display: table;
    table-layout: fixed;
    width: 100%; }
    .mbr-cards-row::after {
      display: none; }
  .mbr-cards-col {
    display: table-cell;
    float: none; }
  .mbr-cards > .container {
    margin: 0;
    max-width: none;
    padding: 0;
    width: 100%; } }
.mbr-cards .card-block {
  background: none;
  padding: 0; }
.mbr-cards .card-img-top {
  border-radius: 0;
  width: auto;
  max-width: 100%; }
.mbr-cards .card-img + .card-block {
  padding-top: 2.3125rem; }
.mbr-cards .card-text {
  margin: 0; }
  .mbr-cards .card-text + .card-btn {
    padding-top: 1.375rem; }
.mbr-cards .card-title {
  margin-bottom: 0;
  font-size: 1.25rem;
  font-weight: bold;
  letter-spacing: -1px;
  line-height: 1.2825; }
  .mbr-cards .card-title + * {
    padding-top: 1rem; }
  .mbr-cards .card-title + .card-btn {
    padding-top: 1.4375rem; }
.mbr-cards .card-subtitle {
  margin-bottom: 0;
  font-style: italic;
  font-weight: 400;
  font-size: 0.875rem;
  font-family: "Lora";
  color: #5b5b5b;
  margin-top: -1rem;
  line-height: 1.7857;
  padding-bottom: 1rem; }
  .mbr-cards .card-subtitle + .card-btn {
    padding-top: 0.3125rem; }
.mbr-cards .iconbox {
  border-style: solid;
  border-color: #f2f2f2;
  border-width: 1px;
  width: 100px;
  height: 100px;
  line-height: 100px;
  border-radius: 500px;
  display: inline-block; }
  .mbr-cards .iconbox > .mbr-iconfont {
    line-height: inherit; }

/*-------

   Price Table

-------*/
@media (min-width: 1600px) {
  .mbr-price-table {
    padding-left: 12%;
    padding-right: 12%; } }
.mbr-price-table .row {
  margin-left: 0;
  margin-right: 0; }
  .mbr-price-table .row > div {
    padding-left: 0.625rem;
    padding-right: 0.625rem; }
    @media (max-width: 1199px) {
      .mbr-price-table .row > div {
        padding-bottom: 40px; } }

/*-------

   Price Table / Plan

-------*/
.mbr-plan {
  background: none;
  border-radius: 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  margin-bottom: 0;
  padding: 0.625rem; }
  .mbr-plan .list-group-item {
    background: none;
    border-bottom: 0;
    border-top: 1px dotted rgba(255, 255, 255, 0.2);
    font-size: 0.875rem;
    line-height: 3.125rem;
    padding-bottom: 0;
    padding-top: 0; }
  .mbr-plan .card-title {
    margin-bottom: 1.5625rem; }
  .mbr-plan-title {
    font-family: "Montserrat";
    font-size: 0.9375rem;
    font-weight: 700;
    line-height: 1.28;
    margin-bottom: 0; }
  .mbr-plan-subtitle, .mbr-plan-price-desc {
    color: #a0a0a0;
    font-family: "Lora";
    font-size: 0.875rem;
    font-style: italic;
    font-weight: 400;
    line-height: 1.79; }
  .mbr-plan-price-desc {
    display: block;
    padding-top: 1.25rem; }
  .mbr-plan-header, .mbr-plan-body {
    background: rgba(255, 255, 255, 0.1); }
  .mbr-plan-header {
    padding-bottom: 3.4375rem;
    padding-top: 2.1875rem;
    position: relative; }
    .mbr-plan-header.bg-primary .mbr-plan-subtitle,
    .mbr-plan-header.bg-primary .mbr-plan-price-desc {
      color: #e8ddcd; }
    .mbr-plan-header.bg-success .mbr-plan-subtitle,
    .mbr-plan-header.bg-success .mbr-plan-price-desc {
      color: #d0dac6; }
    .mbr-plan-header.bg-info .mbr-plan-subtitle,
    .mbr-plan-header.bg-info .mbr-plan-price-desc {
      color: #c7d4d5; }
    .mbr-plan-header.bg-warning .mbr-plan-subtitle,
    .mbr-plan-header.bg-warning .mbr-plan-price-desc {
      color: #fbebc1; }
    .mbr-plan-header.bg-danger .mbr-plan-subtitle,
    .mbr-plan-header.bg-danger .mbr-plan-price-desc {
      color: #fef5f5; }
  .mbr-plan-label {
    background: #28262b;
    color: #fff;
    display: block;
    font-size: 0.6875rem;
    font-weight: 400;
    height: 3.125rem;
    line-height: 3.125rem;
    min-width: 3.125rem;
    padding: 0 0.75rem;
    position: absolute;
    right: 0;
    top: 0; }
  .mbr-plan-body {
    margin-top: 2px;
    padding-bottom: 0;
    padding-top: 2.5rem; }
  .mbr-plan-list {
    padding-bottom: 4.0625rem;
    padding-top: 1.5625rem; }
  .mbr-plan-btn {
    margin-top: -2.5rem;
    padding-bottom: 1.375rem;
    padding-top: 1.875rem; }

/*-------

   Price Table / Price

-------*/
.mbr-price {
  font-family: "Montserrat";
  line-height: 1.3; }
  .mbr-price-value {
    font-size: 1.875rem;
    line-height: 1;
    position: relative;
    top: -1.25rem; }
  .mbr-price-figure {
    font-size: 3.75rem;
    line-height: inherit;
    padding-left: 0.625rem; }
  .mbr-price-term {
    font-size: 1.875rem;
    position: relative; }

/*-------

   Testimonials

-------*/
.mbr-testimonials .row > div {
  padding-bottom: 40px; }
  .mbr-testimonials .row > div:last-child {
    padding-bottom: 0; }
@media (min-width: 992px) {
  .mbr-testimonials .row > div:nth-child(3n+1) {
    clear: left; }
  .mbr-testimonials .row > div:nth-last-child(-1n+3) {
    padding-bottom: 0; } }

/*-------

   Testimonials / Item

-------*/
.mbr-testimonial {
  border-radius: 0;
  border: 0;
  margin-bottom: 0;
  text-align: center; }
  .mbr-testimonial + * {
    padding-top: 40px; }
  .mbr-testimonial.card {
    background-color: transparent; }
  .mbr-testimonial .card-block {
    background: #f4f4f4;
    color: #565656;
    font-family: "Lora";
    font-size: 1.125rem;
    font-style: italic;
    font-weight: 400;
    line-height: 2;
    padding: 1.875rem;
    position: relative; }
    .mbr-testimonial .card-block:after {
      background-color: inherit;
      bottom: -7px;
      content: "";
      display: block;
      height: 14px;
      left: 50%;
      margin-left: -7px;
      position: absolute;
      -webkit-transform: rotate(45deg);
      transform: rotate(45deg);
      width: 14px; }
  .mbr-testimonial-lg .card-block {
    padding-bottom: 90px; }
  .mbr-testimonial .card-footer {
    background: none;
    border: 0;
    padding: 0;
    position: relative;
    z-index: 2; }
    .mbr-testimonial .card-footer:last-child {
      border-radius: 0; }

/*-------

   Testimonials / Author

-------*/
.mbr-author-img {
  margin-top: -2.5rem; }
  .mbr-author-img img {
    height: 5rem;
    width: 5rem; }
.mbr-author-name {
  color: #2d2d2d;
  font-family: "Montserrat";
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: -1px;
  line-height: 1.3;
  padding-top: 1.25rem; }
.mbr-author-desc {
  color: #a7a7a7;
  font-family: "Lora";
  font-size: 0.875rem;
  font-style: italic;
  font-weight: 400;
  line-height: 1.597; }

/*-------

   Info

-------*/
.mbr-info-subtitle, .mbr-info-extra .mbr-info-title {
  margin-bottom: 0; }
.mbr-info-extra .mbr-info-subtitle {
  margin-bottom: 0.5rem; }

/*-------

   Subscribe

-------*/
.mbr-subscribe {
  background: #f5f5f5;
  border-radius: 3px;
  border: 1px solid #e8e8e8;
  height: 4rem;
  padding: 0;
  position: relative; }
  .mbr-subscribe .form-control {
    background: none;
    border: 0;
    position: relative;
    top: 0.4375rem; }
  .mbr-subscribe .input-group-btn {
    position: static; }
    .mbr-subscribe .input-group-btn::after {
      border-radius: 3px;
      border: 1px solid transparent;
      bottom: -1px;
      content: "";
      display: block;
      left: -1px;
      position: absolute;
      right: -1px;
      top: -1px; }
  .form-active .mbr-subscribe .form-control:invalid ~ .input-group-btn::after {
    border-color: #f28281; }
  .mbr-subscribe .btn {
    border-radius: 3px !important;
    margin-bottom: 0;
    margin-right: 0.5625rem; }
  .mbr-subscribe-dark {
    background: none;
    border-color: rgba(255, 255, 255, 0.2); }
    .mbr-subscribe-dark .form-control {
      color: #fff; }

/*-------

   Small footer

-------*/
.mbr-small-footer {
  background: #323232;
  color: #acacac;
  font-size: 0.8125rem;
  padding: 1.75rem 0; }
  .mbr-small-footer p {
    margin-bottom: 0; }

.mbr-small-footer a {
  color: #c0a375; }
  .mbr-small-footer a:hover {
    text-decoration: none; }

/*-------

   Footer

-------*/
.mbr-footer {
  color: #fff;
  padding-left: 1.25rem;
  padding-right: 1.25rem; }
  @media (min-width: 1280px) {
    .mbr-footer {
      padding-left: 6.25rem;
      padding-right: 6.25rem; } }
  .mbr-footer > .row, .mbr-footer > .container > .row {
    margin-left: 0;
    margin-right: 0; }
    .mbr-footer > .row > div, .mbr-footer > .container > .row > div {
      padding-bottom: 40px; }
      .mbr-footer > .row > div:last-child, .mbr-footer > .container > .row > div:last-child {
        padding-bottom: 0; }
  .mbr-footer > .container {
    padding-left: 0;
    padding-right: 0; }
  @media (min-width: 768px) {
    .mbr-footer > .row > div:nth-child(2n+1) {
      clear: left; }
    .mbr-footer > .row > div:nth-last-child(-1n+2) {
      padding-bottom: 0; }
    .mbr-footer > .container > .row > div {
      clear: none !important;
      padding-bottom: 0 !important; } }
  @media (min-width: 992px) {
    .mbr-footer > .row > div {
      clear: none !important;
      padding-bottom: 0 !important; } }
  .mbr-footer p a, .mbr-footer ul a {
    color: #c0a375; }
    .mbr-footer p a:hover, .mbr-footer ul a:hover {
      text-decoration: none; }
  .mbr-footer .btn-black {
    background-color: #242424;
    border-color: #242424; }
    .mbr-footer .btn-black.disabled, .mbr-footer .btn-black[disabled], .mbr-footer .btn-black:hover {
      background-color: #1a1a1a !important;
      border-color: #1a1a1a !important; }
  .mbr-footer .mbr-map {
    height: 18.75rem; }
  .mbr-footer form .form-control-label {
    font-size: 0.875rem;
    position: relative; }
  .mbr-footer form .form-control {
    background: none;
    border-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 0.875rem;
    min-height: 3.2em;
    padding: 0.5em 1em 0.5em; }

.mbr-footer-content h1, .mbr-footer-content h2, .mbr-footer-content h3, .mbr-footer-content h4,
.mbr-footer-content p strong, .mbr-footer-content strong, .mbr-footer .mbr-contacts h1, .mbr-footer .mbr-contacts h2, .mbr-footer .mbr-contacts h3, .mbr-footer .mbr-contacts h4,
.mbr-footer .mbr-contacts p strong, .mbr-footer .mbr-contacts strong {
  color: #7c7c7c;
  font-family: "Montserrat";
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: -1px;
  line-height: 1.3;
  margin-bottom: 1.875em; }
.mbr-footer-content p strong, .mbr-footer-content strong, .mbr-footer .mbr-contacts p strong, .mbr-footer .mbr-contacts strong {
  display: inline-block; }
.mbr-footer-content p, .mbr-footer .mbr-contacts p {
  color: #fff;
  font-size: 0.875rem;
  margin-bottom: 0; }
.mbr-footer-content ul, .mbr-footer .mbr-contacts ul {
  line-height: 1.8;
  list-style: none;
  margin: 0;
  padding: 0; }
.mbr-footer-content li, .mbr-footer .mbr-contacts li {
  border-bottom: 1px dotted rgba(255, 255, 255, 0.2);
  color: #bcbcbc;
  display: block;
  font-family: "Raleway";
  font-size: 0.875rem;
  line-height: 1.8;
  overflow: hidden;
  padding: 0.72em 0;
  padding-left: 1.8em;
  position: relative;
  width: 100%; }
  .mbr-footer-content li:last-child, .mbr-footer .mbr-contacts li:last-child {
    border-bottom: 0; }
  .mbr-footer-content li::before, .mbr-footer .mbr-contacts li::before {
    background: #c0a375;
    content: "";
    height: 0.36em;
    left: 0;
    margin-top: -0.18em;
    position: absolute;
    top: 50%;
    width: 0.36em; }
  .mbr-footer-content li a, .mbr-footer .mbr-contacts li a {
    color: #fff;
    -webkit-transition: color .3s ease;
    -moz-transition: color .3s ease;
    transition: color .3s ease; }
    .mbr-footer-content li a:hover, .mbr-footer .mbr-contacts li a:hover {
      color: #c0a375; }

.footer1 ul {
  margin-top: -13px; }

.footer4 > .container > .row {
  margin-left: 0;
  margin-right: 0; }
  .footer4 > .container > .row > div > .row > div:first-child {
    padding-bottom: 40px; }
    .footer4 > .container > .row > div > .row > div:first-child:last-child {
      padding-bottom: 0; }
@media (min-width: 544px) {
  .footer4 > .container > .row > div, .footer4 > .container > .row > div > .row > div {
    clear: none !important;
    padding-bottom: 0 !important; } }

/*-------

   Company

-------*/
.mbr-company {
  background: none;
  border-radius: 0;
  border: 0;
  margin-bottom: 0; }
  .mbr-company .card-img-top {
    height: 58px;
    margin-bottom: 1.57rem;
    width: auto; }
  .mbr-company .card-block {
    background: none;
    padding: 0; }
  .mbr-company .card-text {
    color: #acacac;
    font-size: 0.875rem;
    margin-bottom: 1.57rem; }
  .mbr-company .list-group {
    display: block; }
  .mbr-company .list-group-item {
    background: none;
    border: 0;
    display: table;
    padding: 0;
    padding-bottom: 1.375rem;
    width: 100%; }
  .mbr-company .list-group-icon,
  .mbr-company .list-group-text {
    display: table-cell;
    height: 2.25rem;
    position: relative;
    vertical-align: middle; }
  .mbr-company .list-group-icon {
    padding-right: 1.25rem;
    text-align: center;
    width: 3.5rem; }
  .mbr-company .list-group-text {
    color: #fff;
    font-family: "Raleway";
    font-size: 0.875rem;
    line-height: 1.6;
    position: static;
    vertical-align: middle; }
    .mbr-company .list-group-text::after {
      border-bottom: 1px dotted rgba(255, 255, 255, 0.2);
      bottom: 0.625rem;
      content: "";
      display: block;
      left: 0;
      position: absolute;
      width: 50%; }
  .mbr-company .list-group-item.active, .mbr-company .list-group-item.active:hover {
    background: none; }
  .mbr-company .list-group-item.active .list-group-text {
    color: #c0a375; }
  .mbr-company .list-group-item:last-child {
    padding-bottom: 0; }
    .mbr-company .list-group-item:last-child .list-group-text::after {
      display: none; }

/*-------

   Arrow

-------*/
@-webkit-keyframes floating-arrow {
  from {
    -webkit-transform: translateY(-4px);
    transform: translateY(-4px); }
  65% {
    -webkit-transform: translateY(4px);
    transform: translateY(4px); }
  to {
    -webkit-transform: translateY(-4px);
    transform: translateY(-4px); } }
@keyframes floating-arrow {
  from {
    -webkit-transform: translateY(-4px);
    transform: translateY(-4px); }
  65% {
    -webkit-transform: translateY(4px);
    transform: translateY(4px); }
  to {
    -webkit-transform: translateY(-4px);
    transform: translateY(-4px); } }
@-webkit-keyframes floating-arrow-up {
  0% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px); }
  25% {
    -webkit-transform: translateY(4px);
    transform: translateY(4px); }
  75% {
    -webkit-transform: translateY(-4px);
    transform: translateY(-4px); } }
@keyframes floating-arrow-up {
  0% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px); }
  25% {
    -webkit-transform: translateY(4px);
    transform: translateY(4px); }
  75% {
    -webkit-transform: translateY(-4px);
    transform: translateY(-4px); } }
.mbr-arrow {
  bottom: 48px;
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%; }
  @media (max-width: 991px) {
    .mbr-arrow {
      bottom: 31px; } }
  @media (max-width: 320px) {
    .mbr-arrow {
      bottom: 21px; } }
  @media all and (device-width: 320px) and (device-height: 568px) and (orientation: portrait) {
    .mbr-arrow {
      bottom: 31px; } }
  .mbr-arrow a {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    color: #fff;
    display: inline-block;
    height: 46px;
    outline-style: none !important;
    position: relative;
    text-decoration: none;
    transition: all .3s ease-in-out;
    width: 46px; }
    .mbr-arrow a:hover {
      background: #2d2d2d; }
  .mbr-arrow-icon {
    display: block; }
    .mbr-arrow-icon::before {
      content: "\203a";
      display: inline-block;
      font-family: serif;
      font-size: 32px;
      line-height: 1;
      font-style: normal;
      left: 4px;
      position: relative;
      top: 6px;
      -webkit-transform: rotate(90deg);
      transform: rotate(90deg); }
  .mbr-arrow-floating .mbr-arrow-icon {
    -webkit-animation: floating-arrow 1.6s infinite ease-in-out 0s;
    animation: floating-arrow 1.6s infinite ease-in-out 0s; }

.mbr-arrow-up {
  bottom: 25px;
  right: 90px;
  position: fixed;
  text-align: right;
  z-index: 5000; }
  @media (max-width: 991px) {
    .mbr-arrow-up {
      bottom: 31px; } }
  @media (max-width: 320px) {
    .mbr-arrow-up {
      bottom: 21px; } }
  @media all and (device-width: 320px) and (device-height: 568px) and (orientation: portrait) {
    .mbr-arrow-up {
      bottom: 31px; } }
  .mbr-arrow-up a {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    color: #fff;
    display: inline-block;
    height: 46px;
    outline-style: none !important;
    position: relative;
    text-decoration: none;
    transition: all .3s ease-in-out;
    width: 46px;
    cursor: pointer; }
    .mbr-arrow-up a:hover {
      background: #2d2d2d; }
  .mbr-arrow-up-icon {
    display: block; }
    .mbr-arrow-up-icon::before {
      content: "\203a";
      display: inline-block;
      font-family: serif;
      font-size: 32px;
      line-height: 1;
      font-style: normal;
      position: relative;
      top: 6px;
      left: -4px;
      -webkit-transform: rotate(-90deg);
      transform: rotate(-90deg); }
  .mbr-arrow-up:hover .mbr-arrow-up-icon {
    -webkit-animation: floating-arrow-up 1.6s ease-in-out infinite 0s;
    animation: floating-arrow-up 1.6s ease-in-out infinite 0s; }

@media (max-width: 768px) {
  .mbr-arrow-up {
    right: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    text-align: center;
    bottom: 15px; } }
@media (min-width: 768px) {
  .mbr-right-padding-md-up {
    padding-right: 60px; } }

@media (min-width: 768px) {
  .mbr-left-padding-md-up {
    padding-left: 60px; } }

.mbr-valign-top {
  vertical-align: top; }

.form-asterisk {
  font-family: initial;
  position: absolute;
  top: -2px;
  font-weight: normal; }

.row-sm-offset {
  margin-left: -0.625rem;
  margin-right: -0.625rem; }
  .row-sm-offset > * {
    padding-left: 0.625rem;
    padding-right: 0.625rem; }

@-webkit-keyframes btnCircleLoading {
  from {
    -webkit-transform: rotate(0deg); }
  to {
    -webkit-transform: rotate(359deg); } }
@keyframes btnCircleLoading {
  from {
    transform: rotate(0deg); }
  to {
    transform: rotate(359deg); } }
.btn-loader {
  -webkit-animation: btnCircleLoading .6s infinite linear;
  animation: btnCircleLoading .6s infinite linear;
  border-radius: 50%;
  border: 4px rgba(255, 255, 255, 0.25) solid;
  border-top-color: white;
  display: none;
  height: 26px;
  left: 50%;
  margin-left: -13px;
  margin-top: -13px;
  position: absolute;
  top: 50%;
  width: 26px; }

.btn-loading {
  position: relative; }
  .btn-loading .btn-text {
    visibility: hidden; }
  .btn-loading .btn-loader {
    display: block; }

.hidden {
  visibility: hidden; }

.visible {
  visibility: visible; }

.hamburger-om:after {
  content: "";
  width: 30px;
  height: 3px;
  background-color: #fff;
  position: absolute;
  top: 8px;
  left: 0; }
.hamburger-om:before {
  content: "";
  width: 30px;
  height: 3px;
  background-color: #fff;
  position: absolute;
  top: -8px;
  left: 0; }

.navbar-brand .text-black {
  color: #000 !important; }

.navbar-toggler.collapsed .hum-top, .hum-middle, .hum-bottom {
  position: absolute;
  content: "";
  right: 0;
  width: 20px;
  height: 3px; }

.navbar-toggler.collapsed .hum-top {
  top: -1px; }

.navbar-toggler.collapsed .hum-middle {
  top: 6px; }

.navbar-toggler.collapsed .hum-bottom {
  bottom: 0; }

.navbar-close .close-icon {
  display: block; }

.navbar-close.collapsed .close-icon {
  display: none; }

.navbar-toggler {
  position: relative; }

/*-------

   Iconfont

-------*/
/* iconfont default styling */
/* for buttons */
.mbr-iconfont.mbr-iconfont-btn,
.mbr-buttons__btn .mbr-iconfont {
  padding-right: 0.4em;
  font-size: 1.6em;
  line-height: 0.5em;
  vertical-align: text-bottom;
  position: relative;
  top: -0.12em;
  text-decoration: none; }

/* menu links */
.mbr-iconfont.mbr-iconfont-btn-parent,
.link .mbr-iconfont {
  font-size: 1.4em;
  top: -0.3em;
  position: relative;
  vertical-align: middle; }

/*menu logo */
.mbr-iconfont.mbr-iconfont-menu,
.mbr-iconfont.mbr-iconfont-ext__menu {
  font-size: 37px;
  text-decoration: none;
  color: #fff; }

/* features2, features3 */
.mbr-iconfont.mbr-iconfont-features2,
.mbr-iconfont.mbr-iconfont-features3 {
  font-size: 119px;
  text-decoration: none;
  text-align: center;
  display: block;
  margin-top: 0.2em;
  color: #F1C050; }

/* features4 */
.mbr-iconfont.mbr-iconfont-features4 {
  font-size: 50px;
  text-decoration: none;
  text-align: center;
  display: block;
  color: #000; }

.mbr-iconfont-features7 {
  font-size: 50px;
  text-decoration: none;
  text-align: center;
  display: block;
  color: #fff; }

/* msg-box4 */
.mbr-iconfont.mbr-iconfont-msg-box4,
.mbr-iconfont.mbr-iconfont-msg-box5 {
  font-size: 250px;
  text-decoration: none;
  color: #232323; }

/* header2 */
.mbr-iconfont.mbr-iconfont-header2 {
  font-size: 250px;
  text-decoration: none;
  color: #fff; }

/* contacts1 */
.mbr-iconfont.mbr-iconfont-contacts1 {
  font-size: 119px;
  text-decoration: none;
  color: #9C9C9C; }

/* contacts3 */
.mbr-iconfont.mbr-iconfont-logo-contacts3 {
  font-size: 58px;
  text-decoration: none;
  color: #9C9C9C;
  margin-bottom: 1.57rem; }

.mbr-iconfont-company-contacts3 {
  font-size: 22px;
  color: #cfcfcf; }

.mbr-iconfont-company-contacts5 {
  font-size: 22px;
  color: #000; }

html.is-builder .mbr-cards .card-img + .card-block {
  padding-top: 0; }
html.is-builder .mbr-cards .card-text + .card-btn {
  padding-top: 0; }
html.is-builder .mbr-cards .card-title + * {
  padding-top: 0; }
html.is-builder .mbr-cards .card-title + .card-btn {
  padding-top: 0; }
html.is-builder .mbr-cards .card-subtitle + .card-btn {
  padding-top: 0; }
html.is-builder :not([data-app-remove-it]).card-img + .card-block {
  padding-top: 2.3125rem; }
html.is-builder :not([data-app-remove-it]).card-title + .card-subtitle, html.is-builder :not([data-app-remove-it]).card-title ~ .card-text {
  padding-top: 1rem; }
html.is-builder :not([data-app-remove-it]).card-title ~ .card-btn {
  padding-top: 1.4375rem; }
html.is-builder :not([data-app-remove-it]).card-subtitle + .card-text {
  padding-top: 0; }
html.is-builder :not([data-app-remove-it]).card-subtitle ~ .card-btn {
  padding-top: 0.3125rem; }
html.is-builder :not([data-app-remove-it]).card-text + .card-btn {
  padding-top: 1.375rem; }
html.is-builder .article .lead p {
  margin-top: 0; }
html.is-builder .article div .btn {
  margin-top: 0; }
html.is-builder .article .mbr-section-title:not([data-app-remove-it]) + .lead p {
  margin-top: 1.5625rem; }
html.is-builder .article .mbr-section-title:not([data-app-remove-it]) ~ div .btn {
  margin-top: 2.3125rem; }
html.is-builder .article .lead:not([data-app-remove-it]) + div .btn {
  margin-top: 2.3125rem; }

.article .display-2 {
  margin-bottom: 0; }
.article .mbr-section-title + .mbr-section-subtitle {
  margin-top: 4px; }
.article .mbr-section-title + .lead p {
  margin-top: 1.5625rem; }
.article .mbr-section-title + div .btn {
  margin-top: 2.3125rem; }
.article .lead + div .btn {
  margin-top: 2.3125rem; }
.article .mbr-section-subtitle {
  margin-bottom: 0; }

.content-size {
  width: auto; }

.image-size {
  vertical-align: middle;
  margin: auto;
  text-align: center; }
  .image-size .mbr-figure {
    padding-top: 3px; }

@media (max-width: 768px) {
  .image-size {
    width: 100% !important; } }

/*# sourceMappingURL=style.css.map */
.engine {
	position: absolute;
	text-indent: -2400px;
	text-align: center;
	padding: 0;
	top: 0;
	left: -2400px;
}