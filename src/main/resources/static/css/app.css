/*
* Skeleton V1.2
* Copyright 2011, <PERSON>
* www.getskeleton.com
* Free to use under the MIT license.
* http://www.opensource.org/licenses/mit-license.php
* 6/20/2012
*/


/* Table of Content
==================================================
	#Reset & Basics
	#Basic Styles
	#Site Styles
	#Typography
	#Links
	#Lists
	#Images
	#Buttons
	#Forms
	#Misc */


/* #Reset & Basics (Inspired by <PERSON><PERSON>)
================================================== */
	html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
		margin: 0;
		padding: 0;
		border: 0;
		font-size: 100%;
		font: inherit;
		vertical-align: baseline; }
	article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
		display: block; }
	body {
		line-height: 1; }
	ol, ul {
		list-style: none; }
	blockquote, q {
		quotes: none; }
	blockquote:before, blockquote:after,
	q:before, q:after {
		content: '';
		content: none; }
	table {
		border-collapse: collapse;
		border-spacing: 0; }


/* #Basic Styles
================================================== */
	body {
		background: #fff;
		font: 14px/21px 'Merriweather Sans', "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif;
		color: #444;
		-webkit-font-smoothing: antialiased; /* Fix for webkit rendering */
		-webkit-text-size-adjust: 100%;
 }


/* #Typography
================================================== */
	h1, h2, h3, h4, h5, h6 {
		color: #181818;
		font-family: 'Merriweather Sans', "Georgia", "Times New Roman", serif;
		font-weight: normal; }
	h1 a, h2 a, h3 a, h4 a, h5 a, h6 a { font-weight: inherit; }
	h1 { font-size: 46px; line-height: 50px; margin-bottom: 14px;}
	h2 { font-size: 35px; line-height: 40px; margin-bottom: 10px; }
	h3 { font-size: 28px; line-height: 34px; margin-bottom: 8px; }
	h4 { font-size: 21px; line-height: 30px; margin-bottom: 4px; }
	h5 { font-size: 17px; line-height: 24px; }
	h6 { font-size: 14px; line-height: 21px; }
	.subheader { color: #777; }

	p { margin: 0 0 20px 0; }
	p img { margin: 0; }
	p.lead { font-size: 21px; line-height: 27px; color: #777;  }

	em { font-style: italic; }
	strong { font-weight: bold; color: #333; }
	small { font-size: 80%; }

/*	Blockquotes  */
	blockquote, blockquote p { font-size: 17px; line-height: 24px; color: #777; font-style: italic; }
	blockquote { margin: 0 0 20px; padding: 9px 20px 0 19px; border-left: 1px solid #ddd; }
	blockquote cite { display: block; font-size: 12px; color: #555; }
	blockquote cite:before { content: "\2014 \0020"; }
	blockquote cite a, blockquote cite a:visited, blockquote cite a:visited { color: #555; }

	hr { border: solid #ddd; border-width: 1px 0 0; clear: both; margin: 10px 0 30px; height: 0; }

/* #Lists
================================================== */
	ul, ol { margin-bottom: 20px; }
	ul { list-style: none outside; }
	ol { list-style: decimal; }
	ol, ul.square, ul.circle, ul.disc { margin-left: 30px; }
	ul.square { list-style: square outside; }
	ul.circle { list-style: circle outside; }
	ul.disc { list-style: disc outside; }
	ul ul, ul ol,
	ol ol, ol ul { margin: 4px 0 5px 30px; font-size: 90%;  }
	ul ul li, ul ol li,
	ol ol li, ol ul li { margin-bottom: 6px; }
	li { line-height: 18px; margin-bottom: 12px; }
	ul.large li { line-height: 21px; }
	li p { line-height: 21px; }

/* #Images
================================================== */

	img.scale-with-grid {
		max-width: 100%;
		height: auto; }


/* #Buttons
================================================== */

	.button,
	button,
	input[type="submit"],
	input[type="reset"],
	input[type="button"] {
		background: #eee; /* Old browsers */
		background: #eee -moz-linear-gradient(top, rgba(255,255,255,.2) 0%, rgba(0,0,0,.2) 100%); /* FF3.6+ */
		background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.2)), color-stop(100%,rgba(0,0,0,.2))); /* Chrome,Safari4+ */
		background: #eee -webkit-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Chrome10+,Safari5.1+ */
		background: #eee -o-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Opera11.10+ */
		background: #eee -ms-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* IE10+ */
		background: #eee linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* W3C */
	  border: 1px solid #aaa;
	  border-top: 1px solid #ccc;
	  border-left: 1px solid #ccc;
	  -moz-border-radius: 3px;
	  -webkit-border-radius: 3px;
	  border-radius: 3px;
	  color: #444;
	  display: inline-block;
	  font-size: 11px;
	  font-weight: bold;
	  text-decoration: none;
	  text-shadow: 0 1px rgba(255, 255, 255, .75);
	  cursor: pointer;
	  margin-bottom: 20px;
	  line-height: normal;
	  padding: 8px 10px;
	  font-family: 'Merriweather Sans', "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif; }

	.button:hover,
	button:hover,
	input[type="submit"]:hover,
	input[type="reset"]:hover,
	input[type="button"]:hover {
		color: #222;
		background: #ddd; /* Old browsers */
		background: #ddd -moz-linear-gradient(top, rgba(255,255,255,.3) 0%, rgba(0,0,0,.3) 100%); /* FF3.6+ */
		background: #ddd -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.3)), color-stop(100%,rgba(0,0,0,.3))); /* Chrome,Safari4+ */
		background: #ddd -webkit-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Chrome10+,Safari5.1+ */
		background: #ddd -o-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Opera11.10+ */
		background: #ddd -ms-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* IE10+ */
		background: #ddd linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* W3C */
	  border: 1px solid #888;
	  border-top: 1px solid #aaa;
	  border-left: 1px solid #aaa; }

	.button:active,
	button:active,
	button.active,
	input[type="submit"]:active,
	input[type="reset"]:active,
	input[type="button"]:active {
		border: 1px solid #666;
		background: #ccc; /* Old browsers */
		background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
		background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
		background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
		background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
		background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
		background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ }

	button.disabled,
	button[disabled="disabled"] {
		opacity: 0.85;
	}

	.button.full-width,
	button.full-width,
	input[type="submit"].full-width,
	input[type="reset"].full-width,
	input[type="button"].full-width {
		width: 100%;
		padding-left: 0 !important;
		padding-right: 0 !important;
		text-align: center; }

	/* Fix for odd Mozilla border & padding issues */
	button::-moz-focus-inner,
	input::-moz-focus-inner {
    border: 0;
    padding: 0;
	}


/* #Forms
================================================== */

	form {
		margin-bottom: 20px; }
	fieldset {
		margin-bottom: 20px; }
	input[type="text"],
	input[type="password"],
	input[type="email"],
	textarea,
	select {
		border: 1px solid #ccc;
		padding: 6px 4px;
		outline: none;
		-moz-border-radius: 2px;
		-webkit-border-radius: 2px;
		border-radius: 2px;
		font: 13px 'Merriweather Sans', "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif;
		color: #777;
		margin: 0;
		width: 210px;
		max-width: 100%;
		display: block;
		margin-bottom: 20px;
		background: #fff; }
	select {
		padding: 0; }
	input[type="text"]:focus,
	input[type="password"]:focus,
	input[type="email"]:focus,
	textarea:focus {
		border: 1px solid #aaa;
 		color: #444;
 		-moz-box-shadow: 0 0 3px rgba(0,0,0,.2);
		-webkit-box-shadow: 0 0 3px rgba(0,0,0,.2);
		box-shadow:  0 0 3px rgba(0,0,0,.2); }
	textarea {
		min-height: 60px; }
	label,
	legend {
		display: block;
		font-weight: bold;
		font-size: 13px;  }
	select {
		width: 220px; }
	input[type="checkbox"] {
		display: inline; }
	label span,
	legend span {
		font-weight: normal;
		font-size: 13px;
		color: #444; }

/* #Misc
================================================== */
	.remove-bottom { margin-bottom: 0 !important; }
	.half-bottom { margin-bottom: 10px !important; }
	.add-bottom { margin-bottom: 20px !important; }



/*
* Skeleton V1.2
* Copyright 2011, Dave Gamache
* www.getskeleton.com
* Free to use under the MIT license.
* http://www.opensource.org/licenses/mit-license.php
* 6/20/2012
*/

/* Table of Content
==================================================
	#Site Styles
	#Page Styles
	#Media Queries
	#Font-Face */

/* #Site Styles
================================================== */

/* #Page Styles
================================================== */

/* #Media Queries
================================================== */

	/* Smaller than standard 960 (devices and browsers) */
	@media only screen and (max-width: 959px) {}

	/* Tablet Portrait size to standard 960 (devices and browsers) */
	@media only screen and (min-width: 768px) and (max-width: 959px) {}

	/* All Mobile Sizes (devices and browser) */
	@media only screen and (max-width: 767px) {}

	/* Mobile Landscape Size to Tablet Portrait (devices and browsers) */
	@media only screen and (min-width: 480px) and (max-width: 767px) {}

	/* Mobile Portrait Size to Mobile Landscape Size (devices and browsers) */
	@media only screen and (max-width: 479px) {}


/* #Font-Face
================================================== */
/* 	This is the proper syntax for an @font-face file
		Just create a "fonts" folder at the root,
		copy your FontName into code below and remove
		comment brackets */

/*	@font-face {
	    font-family: 'FontName';
	    src: url('../fonts/FontName.eot');
	    src: url('../fonts/FontName.eot?iefix') format('eot'),
	         url('../fonts/FontName.woff') format('woff'),
	         url('../fonts/FontName.ttf') format('truetype'),
	         url('../fonts/FontName.svg#webfontZam02nTh') format('svg');
	    font-weight: normal;
	    font-style: normal; }
*/
.pln{color:#000}@media screen{.str{color:#080}.kwd{color:#008}.com{color:#800}.typ{color:#606}.lit{color:#066}.pun,.opn,.clo{color:#660}.tag{color:#008}.atn{color:#606}.atv{color:#080}.dec,.var{color:#606}.fun{color:red}}@media print,projection{.str{color:#060}.kwd{color:#006;font-weight:bold}.com{color:#600;font-style:italic}.typ{color:#404;font-weight:bold}.lit{color:#044}.pun,.opn,.clo{color:#440}.tag{color:#006;font-weight:bold}.atn{color:#404}.atv{color:#060}}pre.prettyprint{padding:2px;border:1px solid #888}ol.linenums{margin-top:0;margin-bottom:0}li.L0,li.L1,li.L2,li.L3,li.L5,li.L6,li.L7,li.L8{list-style-type:none}li.L1,li.L3,li.L5,li.L7,li.L9{background:#eee}
/*
* Skeleton V1.2
* Copyright 2011, Dave Gamache
* www.getskeleton.com
* Free to use under the MIT license.
* http://www.opensource.org/licenses/mit-license.php
* 6/20/2012
*/


/* Table of Contents
==================================================
    #Base 960 Grid
    #Tablet (Portrait)
    #Mobile (Portrait)
    #Mobile (Landscape)
    #Clearing */



/* #Base 960 Grid
================================================== */

    .container                                  { position: relative; width: 960px; margin: 0 auto; padding: 0; }
    .container .column,
    .container .columns                         { float: left; display: inline; margin-left: 10px; margin-right: 10px; }
    .row                                        { margin-bottom: 20px; }

    /* Nested Column Classes */
    .column.alpha, .columns.alpha               { margin-left: 0; }
    .column.omega, .columns.omega               { margin-right: 0; }

    /* Base Grid */
    .container .one.column,
    .container .one.columns                     { width: 40px;  }
    .container .two.columns                     { width: 100px; }
    .container .three.columns                   { width: 160px; }
    .container .four.columns                    { width: 220px; }
    .container .five.columns                    { width: 280px; }
    .container .six.columns                     { width: 340px; }
    .container .seven.columns                   { width: 400px; }
    .container .eight.columns                   { width: 460px; }
    .container .nine.columns                    { width: 520px; }
    .container .ten.columns                     { width: 580px; }
    .container .eleven.columns                  { width: 640px; }
    .container .twelve.columns                  { width: 700px; }
    .container .thirteen.columns                { width: 760px; }
    .container .fourteen.columns                { width: 820px; }
    .container .fifteen.columns                 { width: 880px; }
    .container .sixteen.columns                 { width: 940px; }

    .container .one-third.column                { width: 300px; }
    .container .two-thirds.column               { width: 620px; }

    /* Offsets */
    .container .offset-by-one                   { padding-left: 60px;  }
    .container .offset-by-two                   { padding-left: 120px; }
    .container .offset-by-three                 { padding-left: 180px; }
    .container .offset-by-four                  { padding-left: 240px; }
    .container .offset-by-five                  { padding-left: 300px; }
    .container .offset-by-six                   { padding-left: 360px; }
    .container .offset-by-seven                 { padding-left: 420px; }
    .container .offset-by-eight                 { padding-left: 480px; }
    .container .offset-by-nine                  { padding-left: 540px; }
    .container .offset-by-ten                   { padding-left: 600px; }
    .container .offset-by-eleven                { padding-left: 660px; }
    .container .offset-by-twelve                { padding-left: 720px; }
    .container .offset-by-thirteen              { padding-left: 780px; }
    .container .offset-by-fourteen              { padding-left: 840px; }
    .container .offset-by-fifteen               { padding-left: 900px; }



/* #Tablet (Portrait)
================================================== */

    /* Note: Design for a width of 768px */

    @media only screen and (min-width: 768px) and (max-width: 959px) {
        .container                                  { width: 768px; }
        .container .column,
        .container .columns                         { margin-left: 10px; margin-right: 10px;  }
        .column.alpha, .columns.alpha               { margin-left: 0; margin-right: 10px; }
        .column.omega, .columns.omega               { margin-right: 0; margin-left: 10px; }
        .alpha.omega                                { margin-left: 0; margin-right: 0; }

        .container .one.column,
        .container .one.columns                     { width: 28px; }
        .container .two.columns                     { width: 76px; }
        .container .three.columns                   { width: 124px; }
        .container .four.columns                    { width: 172px; }
        .container .five.columns                    { width: 220px; }
        .container .six.columns                     { width: 268px; }
        .container .seven.columns                   { width: 316px; }
        .container .eight.columns                   { width: 364px; }
        .container .nine.columns                    { width: 412px; }
        .container .ten.columns                     { width: 460px; }
        .container .eleven.columns                  { width: 508px; }
        .container .twelve.columns                  { width: 556px; }
        .container .thirteen.columns                { width: 604px; }
        .container .fourteen.columns                { width: 652px; }
        .container .fifteen.columns                 { width: 700px; }
        .container .sixteen.columns                 { width: 748px; }

        .container .one-third.column                { width: 236px; }
        .container .two-thirds.column               { width: 492px; }

        /* Offsets */
        .container .offset-by-one                   { padding-left: 48px; }
        .container .offset-by-two                   { padding-left: 96px; }
        .container .offset-by-three                 { padding-left: 144px; }
        .container .offset-by-four                  { padding-left: 192px; }
        .container .offset-by-five                  { padding-left: 240px; }
        .container .offset-by-six                   { padding-left: 288px; }
        .container .offset-by-seven                 { padding-left: 336px; }
        .container .offset-by-eight                 { padding-left: 384px; }
        .container .offset-by-nine                  { padding-left: 432px; }
        .container .offset-by-ten                   { padding-left: 480px; }
        .container .offset-by-eleven                { padding-left: 528px; }
        .container .offset-by-twelve                { padding-left: 576px; }
        .container .offset-by-thirteen              { padding-left: 624px; }
        .container .offset-by-fourteen              { padding-left: 672px; }
        .container .offset-by-fifteen               { padding-left: 720px; }
    }


/*  #Mobile (Portrait)
================================================== */

    /* Note: Design for a width of 320px */

    @media only screen and (max-width: 767px) {
        .container { width: 300px; }
        .container .columns,
        .container .column { margin: 0; }

        .container .one.column,
        .container .one.columns,
        .container .two.columns,
        .container .three.columns,
        .container .four.columns,
        .container .five.columns,
        .container .six.columns,
        .container .seven.columns,
        .container .eight.columns,
        .container .nine.columns,
        .container .ten.columns,
        .container .eleven.columns,
        .container .twelve.columns,
        .container .thirteen.columns,
        .container .fourteen.columns,
        .container .fifteen.columns,
        .container .sixteen.columns,
        .container .one-third.column,
        .container .two-thirds.column  { width: 300px; }

        /* Offsets */
        .container .offset-by-one,
        .container .offset-by-two,
        .container .offset-by-three,
        .container .offset-by-four,
        .container .offset-by-five,
        .container .offset-by-six,
        .container .offset-by-seven,
        .container .offset-by-eight,
        .container .offset-by-nine,
        .container .offset-by-ten,
        .container .offset-by-eleven,
        .container .offset-by-twelve,
        .container .offset-by-thirteen,
        .container .offset-by-fourteen,
        .container .offset-by-fifteen { padding-left: 0; }

    }


/* #Mobile (Landscape)
================================================== */

    /* Note: Design for a width of 480px */

    @media only screen and (min-width: 480px) and (max-width: 767px) {
        .container { width: 420px; }
        .container .columns,
        .container .column { margin: 0; }

        .container .one.column,
        .container .one.columns,
        .container .two.columns,
        .container .three.columns,
        .container .four.columns,
        .container .five.columns,
        .container .six.columns,
        .container .seven.columns,
        .container .eight.columns,
        .container .nine.columns,
        .container .ten.columns,
        .container .eleven.columns,
        .container .twelve.columns,
        .container .thirteen.columns,
        .container .fourteen.columns,
        .container .fifteen.columns,
        .container .sixteen.columns,
        .container .one-third.column,
        .container .two-thirds.column { width: 420px; }
    }


/* #Clearing
================================================== */

    /* Self Clearing Goodness */
    .container:after { content: "\0020"; display: block; height: 0; clear: both; visibility: hidden; }

    /* Use clearfix class on parent to clear nested columns,
    or wrap each row of columns in a <div class="row"> */
    .clearfix:before,
    .clearfix:after,
    .row:before,
    .row:after {
      content: '\0020';
      display: block;
      overflow: hidden;
      visibility: hidden;
      width: 0;
      height: 0; }
    .row:after,
    .clearfix:after {
      clear: both; }
    .row,
    .clearfix {
      zoom: 1; }

    /* You can also use a <br class="clear" /> to clear columns */
    .clear {
      clear: both;
      display: block;
      overflow: hidden;
      visibility: hidden;
      width: 0;
      height: 0;
    }


/* line 190 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */

/* line 1 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/stylus/lib/functions/index.styl */

/* line 293 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/stylus/lib/functions/index.styl */

/* line 14 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/index.styl */

/* line 14 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/index.styl */

/* line 2 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/border.styl */

/* line 12 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/border.styl */

/* line 3 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/clearfix.styl */

/* line 31 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/clearfix.styl */

/* line 2 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/color-image.styl */

/* line 6 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/color-image.styl */

/* line 6 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/flex.styl */

/* line 197 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/flex.styl */

/* line 94 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/gradients.styl */

/* line 94 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/gradients.styl */

/* line 94 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/config.styl */

/* line 12 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/config.styl */

/* line 2 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/iconic.styl */

/* line 9 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/iconic.styl */

/* line 2 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/image.styl */

/* line 27 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/image.styl */

/* line 27 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/overflow.styl */

/* line 23 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/overflow.styl */

/* line 9 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/positions.styl */

/* line 73 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/positions.styl */

/* line 15 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/reset.styl */

/* line 79 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/reset.styl */

/* line 5 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/index.styl */

/* line 5 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/index.styl */

/* line 2 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/aliases.styl */

/* line 14 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/aliases.styl */

/* line 2 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/ellipsis.styl */

/* line 11 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/ellipsis.styl */

/* line 11 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/hide-text.styl */

/* line 9 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/hide-text.styl */

/* line 9 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/replace-text.styl */

/* line 10 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/text/replace-text.styl */

/* line undefined : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/vendor.styl */

/* line 576 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/vendor.styl */

/* line 576 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/config.styl */

/* line 12 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/config.styl */

/* line 12 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/size.styl */

/* line 33 : /Users/<USER>/.grunt-sources/web/node_modules/grunt-contrib-stylus/node_modules/nib/lib/nib/size.styl */
/*
* Skeleton V1.2
* Copyright 2011, Dave Gamache
* www.getskeleton.com
* Free to use under the MIT license.
* http://www.opensource.org/licenses/mit-license.php
* 6/20/2012
*/


/* Table of Content
==================================================
	#Reset & Basics
	#Basic Styles
	#Site Styles
	#Typography
	#Links
	#Lists
	#Images
	#Buttons
	#Forms
	#Misc */


/* #Reset & Basics (Inspired by E. Meyers)
================================================== */
	html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
		margin: 0;
		padding: 0;
		border: 0;
		font-size: 100%;
		font: inherit;
		vertical-align: baseline; }
	article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
		display: block; }
	body {
		line-height: 1; }
	ol, ul {
		list-style: none; }
	blockquote, q {
		quotes: none; }
	blockquote:before, blockquote:after,
	q:before, q:after {
		content: '';
		content: none; }
	table {
		border-collapse: collapse;
		border-spacing: 0; }


/* #Basic Styles
================================================== */
	body {
		background: #fff;
		font: 14px/21px 'Merriweather Sans', "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif;
		color: #444;
		-webkit-font-smoothing: antialiased; /* Fix for webkit rendering */
		-webkit-text-size-adjust: 100%;
 }


/* #Typography
================================================== */
	h1, h2, h3, h4, h5, h6 {
		color: #181818;
		font-family: 'Merriweather Sans', "Georgia", "Times New Roman", serif;
		font-weight: normal; }
	h1 a, h2 a, h3 a, h4 a, h5 a, h6 a { font-weight: inherit; }
	h1 { font-size: 46px; line-height: 50px; margin-bottom: 14px;}
	h2 { font-size: 35px; line-height: 40px; margin-bottom: 10px; }
	h3 { font-size: 28px; line-height: 34px; margin-bottom: 8px; }
	h4 { font-size: 21px; line-height: 30px; margin-bottom: 4px; }
	h5 { font-size: 17px; line-height: 24px; }
	h6 { font-size: 14px; line-height: 21px; }
	.subheader { color: #777; }

	p { margin: 0 0 20px 0; }
	p img { margin: 0; }
	p.lead { font-size: 21px; line-height: 27px; color: #777;  }

	em { font-style: italic; }
	strong { font-weight: bold; color: #333; }
	small { font-size: 80%; }

/*	Blockquotes  */
	blockquote, blockquote p { font-size: 17px; line-height: 24px; color: #777; font-style: italic; }
	blockquote { margin: 0 0 20px; padding: 9px 20px 0 19px; border-left: 1px solid #ddd; }
	blockquote cite { display: block; font-size: 12px; color: #555; }
	blockquote cite:before { content: "\2014 \0020"; }
	blockquote cite a, blockquote cite a:visited, blockquote cite a:visited { color: #555; }

	hr { border: solid #ddd; border-width: 1px 0 0; clear: both; margin: 10px 0 30px; height: 0; }

/* #Lists
================================================== */
	ul, ol { margin-bottom: 20px; }
	ul { list-style: none outside; }
	ol { list-style: decimal; }
	ol, ul.square, ul.circle, ul.disc { margin-left: 30px; }
	ul.square { list-style: square outside; }
	ul.circle { list-style: circle outside; }
	ul.disc { list-style: disc outside; }
	ul ul, ul ol,
	ol ol, ol ul { margin: 4px 0 5px 30px; font-size: 90%;  }
	ul ul li, ul ol li,
	ol ol li, ol ul li { margin-bottom: 6px; }
	li { line-height: 18px; margin-bottom: 12px; }
	ul.large li { line-height: 21px; }
	li p { line-height: 21px; }

/* #Images
================================================== */

	img.scale-with-grid {
		max-width: 100%;
		height: auto; }


/* #Buttons
================================================== */

	.button,
	button,
	input[type="submit"],
	input[type="reset"],
	input[type="button"] {
		background: #eee; /* Old browsers */
		background: #eee -moz-linear-gradient(top, rgba(255,255,255,.2) 0%, rgba(0,0,0,.2) 100%); /* FF3.6+ */
		background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.2)), color-stop(100%,rgba(0,0,0,.2))); /* Chrome,Safari4+ */
		background: #eee -webkit-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Chrome10+,Safari5.1+ */
		background: #eee -o-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Opera11.10+ */
		background: #eee -ms-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* IE10+ */
		background: #eee linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* W3C */
	  border: 1px solid #aaa;
	  border-top: 1px solid #ccc;
	  border-left: 1px solid #ccc;
	  -moz-border-radius: 3px;
	  -webkit-border-radius: 3px;
	  border-radius: 3px;
	  color: #444;
	  display: inline-block;
	  font-size: 11px;
	  font-weight: bold;
	  text-decoration: none;
	  text-shadow: 0 1px rgba(255, 255, 255, .75);
	  cursor: pointer;
	  margin-bottom: 20px;
	  line-height: normal;
	  padding: 8px 10px;
	  font-family: 'Merriweather Sans', "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif; }

	.button:hover,
	button:hover,
	input[type="submit"]:hover,
	input[type="reset"]:hover,
	input[type="button"]:hover {
		color: #222;
		background: #ddd; /* Old browsers */
		background: #ddd -moz-linear-gradient(top, rgba(255,255,255,.3) 0%, rgba(0,0,0,.3) 100%); /* FF3.6+ */
		background: #ddd -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.3)), color-stop(100%,rgba(0,0,0,.3))); /* Chrome,Safari4+ */
		background: #ddd -webkit-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Chrome10+,Safari5.1+ */
		background: #ddd -o-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Opera11.10+ */
		background: #ddd -ms-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* IE10+ */
		background: #ddd linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* W3C */
	  border: 1px solid #888;
	  border-top: 1px solid #aaa;
	  border-left: 1px solid #aaa; }

	.button:active,
	button:active,
	button.active,
	input[type="submit"]:active,
	input[type="reset"]:active,
	input[type="button"]:active {
		border: 1px solid #666;
		background: #ccc; /* Old browsers */
		background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
		background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
		background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
		background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
		background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
		background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ }

	button.disabled,
	button[disabled="disabled"] {
		opacity: 0.85;
	}

	.button.full-width,
	button.full-width,
	input[type="submit"].full-width,
	input[type="reset"].full-width,
	input[type="button"].full-width {
		width: 100%;
		padding-left: 0 !important;
		padding-right: 0 !important;
		text-align: center; }

	/* Fix for odd Mozilla border & padding issues */
	button::-moz-focus-inner,
	input::-moz-focus-inner {
    border: 0;
    padding: 0;
	}


/* #Forms
================================================== */

	form {
		margin-bottom: 20px; }
	fieldset {
		margin-bottom: 20px; }
	input[type="text"],
	input[type="password"],
	input[type="email"],
	textarea,
	select {
		border: 1px solid #ccc;
		padding: 6px 4px;
		outline: none;
		-moz-border-radius: 2px;
		-webkit-border-radius: 2px;
		border-radius: 2px;
		font: 13px 'Merriweather Sans', "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif;
		color: #777;
		margin: 0;
		width: 210px;
		max-width: 100%;
		display: block;
		margin-bottom: 20px;
		background: #fff; }
	select {
		padding: 0; }
	input[type="text"]:focus,
	input[type="password"]:focus,
	input[type="email"]:focus,
	textarea:focus {
		border: 1px solid #aaa;
 		color: #444;
 		-moz-box-shadow: 0 0 3px rgba(0,0,0,.2);
		-webkit-box-shadow: 0 0 3px rgba(0,0,0,.2);
		box-shadow:  0 0 3px rgba(0,0,0,.2); }
	textarea {
		min-height: 60px; }
	label,
	legend {
		display: block;
		font-weight: bold;
		font-size: 13px;  }
	select {
		width: 220px; }
	input[type="checkbox"] {
		display: inline; }
	label span,
	legend span {
		font-weight: normal;
		font-size: 13px;
		color: #444; }

/* #Misc
================================================== */
	.remove-bottom { margin-bottom: 0 !important; }
	.half-bottom { margin-bottom: 10px !important; }
	.add-bottom { margin-bottom: 20px !important; }

/* line 33 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/vendor/base.css */
/*
* Skeleton V1.2
* Copyright 2011, Dave Gamache
* www.getskeleton.com
* Free to use under the MIT license.
* http://www.opensource.org/licenses/mit-license.php
* 6/20/2012
*/


/* Table of Contents
==================================================
    #Base 960 Grid
    #Tablet (Portrait)
    #Mobile (Portrait)
    #Mobile (Landscape)
    #Clearing */



/* #Base 960 Grid
================================================== */

    .container                                  { position: relative; width: 960px; margin: 0 auto; padding: 0; }
    .container .column,
    .container .columns                         { float: left; display: inline; margin-left: 10px; margin-right: 10px; }
    .row                                        { margin-bottom: 20px; }

    /* Nested Column Classes */
    .column.alpha, .columns.alpha               { margin-left: 0; }
    .column.omega, .columns.omega               { margin-right: 0; }

    /* Base Grid */
    .container .one.column,
    .container .one.columns                     { width: 40px;  }
    .container .two.columns                     { width: 100px; }
    .container .three.columns                   { width: 160px; }
    .container .four.columns                    { width: 220px; }
    .container .five.columns                    { width: 280px; }
    .container .six.columns                     { width: 340px; }
    .container .seven.columns                   { width: 400px; }
    .container .eight.columns                   { width: 460px; }
    .container .nine.columns                    { width: 520px; }
    .container .ten.columns                     { width: 580px; }
    .container .eleven.columns                  { width: 640px; }
    .container .twelve.columns                  { width: 700px; }
    .container .thirteen.columns                { width: 760px; }
    .container .fourteen.columns                { width: 820px; }
    .container .fifteen.columns                 { width: 880px; }
    .container .sixteen.columns                 { width: 940px; }

    .container .one-third.column                { width: 300px; }
    .container .two-thirds.column               { width: 620px; }

    /* Offsets */
    .container .offset-by-one                   { padding-left: 60px;  }
    .container .offset-by-two                   { padding-left: 120px; }
    .container .offset-by-three                 { padding-left: 180px; }
    .container .offset-by-four                  { padding-left: 240px; }
    .container .offset-by-five                  { padding-left: 300px; }
    .container .offset-by-six                   { padding-left: 360px; }
    .container .offset-by-seven                 { padding-left: 420px; }
    .container .offset-by-eight                 { padding-left: 480px; }
    .container .offset-by-nine                  { padding-left: 540px; }
    .container .offset-by-ten                   { padding-left: 600px; }
    .container .offset-by-eleven                { padding-left: 660px; }
    .container .offset-by-twelve                { padding-left: 720px; }
    .container .offset-by-thirteen              { padding-left: 780px; }
    .container .offset-by-fourteen              { padding-left: 840px; }
    .container .offset-by-fifteen               { padding-left: 900px; }



/* #Tablet (Portrait)
================================================== */

    /* Note: Design for a width of 768px */

    @media only screen and (min-width: 768px) and (max-width: 959px) {
        .container                                  { width: 768px; }
        .container .column,
        .container .columns                         { margin-left: 10px; margin-right: 10px;  }
        .column.alpha, .columns.alpha               { margin-left: 0; margin-right: 10px; }
        .column.omega, .columns.omega               { margin-right: 0; margin-left: 10px; }
        .alpha.omega                                { margin-left: 0; margin-right: 0; }

        .container .one.column,
        .container .one.columns                     { width: 28px; }
        .container .two.columns                     { width: 76px; }
        .container .three.columns                   { width: 124px; }
        .container .four.columns                    { width: 172px; }
        .container .five.columns                    { width: 220px; }
        .container .six.columns                     { width: 268px; }
        .container .seven.columns                   { width: 316px; }
        .container .eight.columns                   { width: 364px; }
        .container .nine.columns                    { width: 412px; }
        .container .ten.columns                     { width: 460px; }
        .container .eleven.columns                  { width: 508px; }
        .container .twelve.columns                  { width: 556px; }
        .container .thirteen.columns                { width: 604px; }
        .container .fourteen.columns                { width: 652px; }
        .container .fifteen.columns                 { width: 700px; }
        .container .sixteen.columns                 { width: 748px; }

        .container .one-third.column                { width: 236px; }
        .container .two-thirds.column               { width: 492px; }

        /* Offsets */
        .container .offset-by-one                   { padding-left: 48px; }
        .container .offset-by-two                   { padding-left: 96px; }
        .container .offset-by-three                 { padding-left: 144px; }
        .container .offset-by-four                  { padding-left: 192px; }
        .container .offset-by-five                  { padding-left: 240px; }
        .container .offset-by-six                   { padding-left: 288px; }
        .container .offset-by-seven                 { padding-left: 336px; }
        .container .offset-by-eight                 { padding-left: 384px; }
        .container .offset-by-nine                  { padding-left: 432px; }
        .container .offset-by-ten                   { padding-left: 480px; }
        .container .offset-by-eleven                { padding-left: 528px; }
        .container .offset-by-twelve                { padding-left: 576px; }
        .container .offset-by-thirteen              { padding-left: 624px; }
        .container .offset-by-fourteen              { padding-left: 672px; }
        .container .offset-by-fifteen               { padding-left: 720px; }
    }


/*  #Mobile (Portrait)
================================================== */

    /* Note: Design for a width of 320px */

    @media only screen and (max-width: 767px) {
        .container { width: 300px; }
        .container .columns,
        .container .column { margin: 0; }

        .container .one.column,
        .container .one.columns,
        .container .two.columns,
        .container .three.columns,
        .container .four.columns,
        .container .five.columns,
        .container .six.columns,
        .container .seven.columns,
        .container .eight.columns,
        .container .nine.columns,
        .container .ten.columns,
        .container .eleven.columns,
        .container .twelve.columns,
        .container .thirteen.columns,
        .container .fourteen.columns,
        .container .fifteen.columns,
        .container .sixteen.columns,
        .container .one-third.column,
        .container .two-thirds.column  { width: 300px; }

        /* Offsets */
        .container .offset-by-one,
        .container .offset-by-two,
        .container .offset-by-three,
        .container .offset-by-four,
        .container .offset-by-five,
        .container .offset-by-six,
        .container .offset-by-seven,
        .container .offset-by-eight,
        .container .offset-by-nine,
        .container .offset-by-ten,
        .container .offset-by-eleven,
        .container .offset-by-twelve,
        .container .offset-by-thirteen,
        .container .offset-by-fourteen,
        .container .offset-by-fifteen { padding-left: 0; }

    }


/* #Mobile (Landscape)
================================================== */

    /* Note: Design for a width of 480px */

    @media only screen and (min-width: 480px) and (max-width: 767px) {
        .container { width: 420px; }
        .container .columns,
        .container .column { margin: 0; }

        .container .one.column,
        .container .one.columns,
        .container .two.columns,
        .container .three.columns,
        .container .four.columns,
        .container .five.columns,
        .container .six.columns,
        .container .seven.columns,
        .container .eight.columns,
        .container .nine.columns,
        .container .ten.columns,
        .container .eleven.columns,
        .container .twelve.columns,
        .container .thirteen.columns,
        .container .fourteen.columns,
        .container .fifteen.columns,
        .container .sixteen.columns,
        .container .one-third.column,
        .container .two-thirds.column { width: 420px; }
    }


/* #Clearing
================================================== */

    /* Self Clearing Goodness */
    .container:after { content: "\0020"; display: block; height: 0; clear: both; visibility: hidden; }

    /* Use clearfix class on parent to clear nested columns,
    or wrap each row of columns in a <div class="row"> */
    .clearfix:before,
    .clearfix:after,
    .row:before,
    .row:after {
      content: '\0020';
      display: block;
      overflow: hidden;
      visibility: hidden;
      width: 0;
      height: 0; }
    .row:after,
    .clearfix:after {
      clear: both; }
    .row,
    .clearfix {
      zoom: 1; }

    /* You can also use a <br class="clear" /> to clear columns */
    .clear {
      clear: both;
      display: block;
      overflow: hidden;
      visibility: hidden;
      width: 0;
      height: 0;
    }

/* line 33 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/vendor/skeleton.css */
/*
* Skeleton V1.2
* Copyright 2011, Dave Gamache
* www.getskeleton.com
* Free to use under the MIT license.
* http://www.opensource.org/licenses/mit-license.php
* 6/20/2012
*/

/* Table of Content
==================================================
	#Site Styles
	#Page Styles
	#Media Queries
	#Font-Face */

/* #Site Styles
================================================== */

/* #Page Styles
================================================== */

/* #Media Queries
================================================== */

	/* Smaller than standard 960 (devices and browsers) */
	@media only screen and (max-width: 959px) {}

	/* Tablet Portrait size to standard 960 (devices and browsers) */
	@media only screen and (min-width: 768px) and (max-width: 959px) {}

	/* All Mobile Sizes (devices and browser) */
	@media only screen and (max-width: 767px) {}

	/* Mobile Landscape Size to Tablet Portrait (devices and browsers) */
	@media only screen and (min-width: 480px) and (max-width: 767px) {}

	/* Mobile Portrait Size to Mobile Landscape Size (devices and browsers) */
	@media only screen and (max-width: 479px) {}


/* #Font-Face
================================================== */
/* 	This is the proper syntax for an @font-face file
		Just create a "fonts" folder at the root,
		copy your FontName into code below and remove
		comment brackets */

/*	@font-face {
	    font-family: 'FontName';
	    src: url('../fonts/FontName.eot');
	    src: url('../fonts/FontName.eot?iefix') format('eot'),
	         url('../fonts/FontName.woff') format('woff'),
	         url('../fonts/FontName.ttf') format('truetype'),
	         url('../fonts/FontName.svg#webfontZam02nTh') format('svg');
	    font-weight: normal;
	    font-style: normal; }
*/

/* line 33 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/vendor/layout.css */
.pln{color:#000}@media screen{.str{color:#080}.kwd{color:#008}.com{color:#800}.typ{color:#606}.lit{color:#066}.pun,.opn,.clo{color:#660}.tag{color:#008}.atn{color:#606}.atv{color:#080}.dec,.var{color:#606}.fun{color:red}}@media print,projection{.str{color:#060}.kwd{color:#006;font-weight:bold}.com{color:#600;font-style:italic}.typ{color:#404;font-weight:bold}.lit{color:#044}.pun,.opn,.clo{color:#440}.tag{color:#006;font-weight:bold}.atn{color:#404}.atv{color:#060}}pre.prettyprint{padding:2px;border:1px solid #888}ol.linenums{margin-top:0;margin-bottom:0}li.L0,li.L1,li.L2,li.L3,li.L5,li.L6,li.L7,li.L8{list-style-type:none}li.L1,li.L3,li.L5,li.L7,li.L9{background:#eee}

/* line 14 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
h3 {
  text-decoration: underline;
}

/* line 16 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
b {
  font-weight: bold;
}

/* line 18 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
i {
  font-style: italic !important;
}

/* line 21 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
input:focus,
button {
  outline: none;
}

/* line 23 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
input[type="radio"],
label[for] {
  cursor: pointer;
}

/* line 25 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.main {
  position: relative;
  margin-top: 100px;
  margin-bottom: 100px;
}

/* line 29 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.center {
  text-align: center;
}

/* line 31 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.left {
  text-align: left;
}

/* line 33 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.right {
  text-align: right;
}

/* line 35 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.italic {
  font-style: italic;
}

/* line 37 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.bold {
  font-weight: bold;
}

/* line 39 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.inline {
  display: inline;
}

/* line 41 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.sixteen.columns {
  margin-bottom: 50px;
}

/* line 43 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.section {
  margin-top: 20px;
}

/* line 47 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
a,
a:visited {
  color: #080;
  outline: 0;
  text-decoration: none;
}

/* line 51 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */

/* line 52 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
a:hover,
a:focus {
  color: #040;
}

/* line 54 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
p a,
p a:visited {
  line-height: inherit;
}

/* line 58 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.indent1 {
  margin-left: 10px;
}

/* line 60 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.indent2 {
  margin-left: 20px;
}

/* line 64 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
code {
  color: #080;
}

/* line 66 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
code.black {
  color: #181818;
}

/* line 83 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
pre.prettyprint {
  word-wrap: break-word;
  font-family: 'Droid Sans Mono', Courier;
  border: none;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -ms-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
  padding: 10px;
  margin-bottom: 10px;
}

/* line 90 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
pre.prettyprint.runnable {
  border-left: #080 1px solid;
  cursor: pointer;
}

/* line 93 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
pre.prettyprint:hover {
  background: whiteSmoke;
}

/* line 95 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
pre.prettyprint.running {
  background: #d3f3d3;
}

/* line 98 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.pos-chooser-dial {
  cursor: pointer;
}

/* line 100 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.box.pos-chooser-demo {
  color: #000;
  padding: 20px;
}

/* line 104 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.entry {
  margin-left: 10px;
  padding: 10px;
}

/* line 107 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.entry:hover {
  background: whiteSmoke;
}

/* line 110 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.demo-mouse {
  position: relative;
  left: 250px;
  top: 30px;
  -webkit-transition: all 1s ease-out;
  -moz-transition: all 1s ease-out;
  -o-transition: all 1s ease-out;
  -ms-transition: all 1s ease-out;
  transition: all 1s ease-out;
}

/* line 113 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.demo-mouse.over {
  top: -30px;
  left: 125px;
}

/* line 116 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.box {
  color: #080;
  font-weight: bold;
  border: 1px solid #080;
  padding: 3px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
}

/* line 123 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.build-tool-toggle {
  margin: 20px;
  display: none;
  -webkit-box-shadow: 1px 1px 30px 1px #080;
  box-shadow: 1px 1px 30px 1px #080;
}

/* line 127 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.columns.build-tool {
  display: none;
  background: whiteSmoke;
  padding: 25px 25px 5px 25px;
}

/* line 131 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.columns.build-tool input,
.columns.build-tool textarea,
.columns.build-tool button,
.columns.build-tool select {
  margin-bottom: 5px;
}

/* line 133 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.columns.build-tool hr {
  margin-bottom: 15px;
}

/* line 135 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.columns.build-tool select {
  display: inline;
  height: 32px;
}

/* line 138 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.columns.build-tool .build-tool-style {
  padding: 3px;
  margin: 5px 0;
  background: #ccc;
  -webkit-border-radius: 3px;
  border-radius: 3px;
}

/* line 143 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.columns.build-tool .build-tool-style .name {
  display: inline;
}

/* line 145 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.columns.build-tool .build-tool-style .name,
.columns.build-tool .build-tool-style .code {
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/* line 148 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.columns.build-tool .build-tool-style .code {
  font-family: 'Droid Sans Mono', Courier;
  height: 215px;
}

/* line 151 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.notifyjs-gswg-base {
  font-family: Helvetica, Arial;
  opacity: 0.85;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
  filter: alpha(opacity=85);
  width: 325px;
  background: #e48632;
  padding: 15px;
  -webkit-border-radius: 10px;
  border-radius: 10px;
}

/* line 158 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */

/* line 159 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.notifyjs-gswg-base .content .cover {
  float: left;
  background: url("http://gswg.io/img/cover2.jpg");
  -webkit-background-size: cover;
  -moz-background-size: cover;
  background-size: cover;
  width: 105px;
  height: 130px;
}

/* line 165 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.notifyjs-gswg-base .content .text {
  float: left;
  width: 200px;
  padding-left: 15px;
  padding-top: 10px;
  color: #fff;
}

/* line 171 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.notifyjs-gswg-base .content .text .title {
  font-weight: bold;
}

/* line 173 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.notifyjs-gswg-base .content .text button {
  margin-bottom: 2px;
}

/* line 176 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

/* line 183 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.clearfix {
  display: inline-block;
}

/* line 69 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
/* start commented backslash hack \*/

/* line 186 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
* html .clearfix {
  height: 1%;
}

/* line 188 : /Users/<USER>/Code/JavaScript/notifyjs/docs/src/styles/app.styl */
.clearfix {
  display: block;
}
