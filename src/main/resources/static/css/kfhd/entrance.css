@charset "utf-8";
/* CSS Document */

#wrapper{    
    height: 90%;
	width: 90%;
	position: absolute;
	/* Center align this Div half of remaining height and width [(100 - 90) / 2]*/
   	top: 5%;
   	left: 5%;
}

body {
	background-image: url(../../images/system/entrance_screen.png) no-repeat center center fixed;
	-ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../images/system/entrance_screen.png',sizingMethod='scale')";
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: 90% 98%;
	background-color: green;
	width: 100%;	
	}
	
/* portate mode */
@media (max-width: 800px){	
	body {
	background-image: url(../../images/system/entrance_screen.png) no-repeat center center fixed;
	-ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../images/system/entrance_screen.png',sizingMethod='scale')";
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: 95% 92%;
	background-color: pink;
	width: 100%;	
	}	
}

a:link {color: #FFF; text-decoration: none; }
a:active {color: #FFF; text-decoration: underline; }
a:visited {color: #CCC; text-decoration: underline; }
a:hover {color: #FFF; text-decoration: underline;}

#flags {
    position:absolute;
    top:0;
    left:3px;	
	}

#infoTXT{
	font-family:Verdana, Geneva, sans-serif;
	font-size:10px;
	color:#FFF;
	}

#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 25%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:1.5em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}

/* portate mode */
@media (max-width: 800px){	
#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 25%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:1.4em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}	
}

/* same style a copyright */
#footer{
	font-family:Verdana, Geneva, sans-serif;
	font-size:10px;
	color:#FFF;
	
    position:absolute;
    bottom:5px;
    right:10px;
	}
	
#copyright{
	font-family:Verdana, Geneva, sans-serif;
	font-size:10px;
	color:#FFF;
	
    position:absolute;
    bottom:5px;
    right:20px;
    width: 162px;
	}

/* portate mode */
@media (max-width: 800px){	
#copyright{
	font-family:Verdana, Geneva, sans-serif;
	font-size:10px;
	color:#FFF;
	
    position:absolute;
    bottom:5px;
    right:15px;
    width: 190px;
	}	
}

/* #### CSS that's applied when the viewing area's width is 765px or less #### */
/*@media screen and (max-width: 1920px)and (min-width: 720px){    */
#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:7%;
	height:5%;
	font-size:1.4em;
	color:#FFF;
	
	position: absolute;
   	top: 50%;
   	left: 50%;
   	margin-top: 2.2%; /* Half the height */
   	margin-left: -4.25%; /* Half the width */		
		
	text-align:center;

	display:block;
        -webkit-transform: rotate(12deg);
        -webkit-transform:margin-top(20%); /* Half the height */
   		-webkit-transform:margin-left(-4.25%); /* Half the width */
        
        -moz-transform: rotate(12deg); 
		-moz-transform: margin-top(2%); /* Half the height */
   		-moz-transform: margin-left(-4.25%); /* Half the width */
        
    /* IE */
	-ms-transform: rotate(-90deg);
	
	-ms-transform:margin-top(2%); /* Half the height */
   	-ms-transform:margin-left(-4.25%); /* Half the width */

	/* Opera */
	-o-transform: rotate(-90deg);

	/* Internet Explorer */
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
        
	}

/* portate mode */
@media (max-width: 800px){	
#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:7%;
	height:5%;
	font-size:1.2em;
	color:#FFF;
	
	position: absolute;
   	top: 53%;
   	left: 49.5%;
   	margin-top: 2.2%; /* Half the height */
   	margin-left: -4.25%; /* Half the width */		
		
	text-align:center;

	display:block;
        -webkit-transform: rotate(12deg);
        -webkit-transform:margin-top(20%); /* Half the height */
   		-webkit-transform:margin-left(-4.25%); /* Half the width */
        
        -moz-transform: rotate(12deg); 
		-moz-transform: margin-top(2%); /* Half the height */
   		-moz-transform: margin-left(-4.25%); /* Half the width */
        
    /* IE */
	-ms-transform: rotate(-90deg);
	
	-ms-transform:margin-top(2%); /* Half the height */
   	-ms-transform:margin-left(-4.25%); /* Half the width */

	/* Opera */
	-o-transform: rotate(-90deg);

	/* Internet Explorer */
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
        
	}	
}

#signReg{
	position:absolute;
    top:0;
    left:3px;	
    margin-top:22px;
    font-size:10px;
	color:#FFF;
}

#content-enter{
	content_mast {padding: 0 8px 4px 8px;
	border-bottom: 1px solid #e6e6e6;
	height:112px;
	background-color:#00B;
}
