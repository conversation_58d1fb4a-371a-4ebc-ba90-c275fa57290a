@charset "utf-8";
/* CSS Document */


@media (max-width: 699px) and (min-width: 520px) {
  @-viewport {
    width: 640px;
    zoom: .5;
  }
}

#wrapper{    
    height: 90%;
	width: 90%;
	position: absolute;
	/* Center align this Div half of remaining height and width [(100 - 90) / 2]*/
   	top: 5%;
   	left: 5%;
}

/** Need to add un-annotated style version for browsers not following W3C **/

body {
	background: url(../images/system/entrance_screen.png) no-repeat center center fixed;
	-ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../images/system/entrance_screen.png',sizingMethod='scale')";
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: 90% 98%;
	background-color: black;
	width: 100%;	
	}


#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 25%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:2.2em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}

#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:9%;
	height:7%;
	font-size:1.8em;
	color:#FFF;
	
	position: absolute;
   	top: 52%;
   	left: 49%;
   	margin-top: 2%; /* Half the height */
   	margin-left: -4.25%; /* Half the width */		
		
	text-align:center;

	display:block;
        -webkit-transform: rotate(12deg);
        -webkit-transform:margin-top(20%); /* Half the height */
   		-webkit-transform:margin-left(-4.25%); /* Half the width */
        
        -moz-transform: rotate(12deg); 
		-moz-transform: margin-top(2%); /* Half the height */
   		-moz-transform: margin-left(-4.25%); /* Half the width */
        
    /* IE */
	-ms-transform: rotate(-90deg);
	
	-ms-transform:margin-top(2%); /* Half the height */
   	-ms-transform:margin-left(-4.25%); /* Half the width */

	/* Opera */
	-o-transform: rotate(-90deg);

	/* Internet Explorer */
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
        
	}

@media (max-width: 1200px) and (min-width: 640px) {
body {
	background: url(../images/system/entrance_screen.png) no-repeat center center fixed;
	-ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../images/system/entrance_screen.png',sizingMethod='scale')";
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: 90% 98%;
	background-color: black;
	width: 100%;	
	}
}

a:link {color: #FFF; text-decoration: none; border-style: none;}
a:active {color: #FFF; text-decoration: underline; }
a:visited {color: #CCC; text-decoration: underline; }
a:hover {color: #FFF; text-decoration: underline; border-style: dotted;}

#flags {
    position:absolute;
    top:0;
    left:3px;	
	}

#infoTXT{
	font-family:Verdana, Geneva, sans-serif;
	font-size:10px;
	color:#FFF;
	}

@media screen and (max-width: 615px) {
#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 25%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:1.2em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}
}	

@media screen and (max-width: 615px) {
#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:7%;
	height:5%;
	font-size:0.6em;
	color:#FFF;
	
	position: absolute;
   	top: 52%;
   	left: 49%;
   	margin-top: 2%; /* Half the height */
   	margin-left: -4.25%; /* Half the width */		
		
	text-align:center;

	display:block;
        -webkit-transform: rotate(12deg);
        -webkit-transform:margin-top(20%); /* Half the height */
   		-webkit-transform:margin-left(-4.25%); /* Half the width */
        
        -moz-transform: rotate(12deg); 
		-moz-transform: margin-top(2%); /* Half the height */
   		-moz-transform: margin-left(-4.25%); /* Half the width */
        
    /* IE */
	-ms-transform: rotate(-90deg);
	
	-ms-transform:margin-top(2%); /* Half the height */
   	-ms-transform:margin-left(-4.25%); /* Half the width */

	/* Opera */
	-o-transform: rotate(-90deg);

	/* Internet Explorer */
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
        
	}
}

@media screen and (min-width: 616px) and (max-width: 800px) {
#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 25%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:1.3em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}
}

@media screen and (min-width: 616px) and (max-width: 800px) {
#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:7%;
	height:5%;
	font-size:1.2em;
	color:#FFF;
	
	position: absolute;
   	top: 52%;
   	left: 49%;
   	margin-top: 1.4%; /* Half the height */
   	margin-left: -4.25%; /* Half the width */		
		
	text-align:center;

	display:block;
        -webkit-transform: rotate(12deg);
        -webkit-transform:margin-top(20%); /* Half the height */
   		-webkit-transform:margin-left(-4.25%); /* Half the width */
        
        -moz-transform: rotate(12deg); 
		-moz-transform: margin-top(2%); /* Half the height */
   		-moz-transform: margin-left(-4.25%); /* Half the width */
        
    /* IE */
	-ms-transform: rotate(-90deg);
	
	-ms-transform:margin-top(2%); /* Half the height */
   	-ms-transform:margin-left(-4.25%); /* Half the width */

	/* Opera */
	-o-transform: rotate(-90deg);

	/* Internet Explorer */
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
        
	}
}


@media screen and (min-width: 801px) and (max-width: 1200px) {
#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 25%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:1.9em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}
}

@media screen and (min-width: 801px) and (max-width: 1200px) {
#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:7%;
	height:5%;
	font-size:1.6em;
	color:#FFF;
	
	position: absolute;
   	top: 52%;
   	left: 49%;
   	margin-top: 0.7%; /* Half the height */
   	margin-left: -4.25%; /* Half the width */		
		
	text-align:center;

	display:block;
        -webkit-transform: rotate(12deg);
        -webkit-transform:margin-top(20%); /* Half the height */
   		-webkit-transform:margin-left(-4.25%); /* Half the width */
        
        -moz-transform: rotate(12deg); 
		-moz-transform: margin-top(2%); /* Half the height */
   		-moz-transform: margin-left(-4.25%); /* Half the width */
        
    /* IE */
	-ms-transform: rotate(-90deg);
	
	-ms-transform:margin-top(2%); /* Half the height */
   	-ms-transform:margin-left(-4.25%); /* Half the width */

	/* Opera */
	-o-transform: rotate(-90deg);

	/* Internet Explorer */
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
        
	}
}


@media screen and (min-width: 1201px) and (max-width: 2000px) {
#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 25%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:2.5em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}
}	

@media screen and (min-width: 1201px) and (max-width: 2000px) {
#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:7%;
	height:5%;
	font-size:2.5em;
	color:#FFF;
	
	position: absolute;
   	top: 52%;
   	left: 49%;
   	margin-top: 0.8%; /* Half the height */
   	margin-left: -4.25%; /* Half the width */		
		
	text-align:center;

	display:block;
        -webkit-transform: rotate(12deg);
        -webkit-transform:margin-top(20%); /* Half the height */
   		-webkit-transform:margin-left(-4.25%); /* Half the width */
        
        -moz-transform: rotate(12deg); 
		-moz-transform: margin-top(2%); /* Half the height */
   		-moz-transform: margin-left(-4.25%); /* Half the width */
        
    /* IE */
	-ms-transform: rotate(-90deg);
	
	-ms-transform:margin-top(2%); /* Half the height */
   	-ms-transform:margin-left(-4.25%); /* Half the width */

	/* Opera */
	-o-transform: rotate(-90deg);

	/* Internet Explorer */
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
        
	}
}



#copyright{
	font-family:Verdana, Geneva, sans-serif;
	font-size:10px;
	color:#FFF;
	
    position:absolute;
    bottom:5px;
    right:20px;
    width: 162px;
}

#signReg{
	position:absolute;
    top:0;
    left:3px;	
    margin-top:22px;
    font-size:10px;
	color:#FFF;
}

#content-enter{
	content_mast {padding: 0 8px 4px 8px;
	border-bottom: 1px solid #e6e6e6;
	height:112px;
	background-color:#00B;
}

@media only screen and (max-device-width: 640px) {
	body {
		background-color: pink;
	}
}

.vcenter {
    display: inline-block;
    vertical-align: middle;
    float: none;
}
.ion-ios-analytics-outline2{
	background: url(../images/system/whiteout-tvlogo-Only.svg) no-repeat center center fixed;
	background-size: 90% 98%;
}