/** MyEclipse for Spring additions to the iWebKit 5.04 style sheet */

.smallfield, li.smallfield {
	list-style-type: none;
	display: block;
	height: 50px;
	padding: 5px 9px;
	position: relative;
	overflow: hidden;
	border-top: 1px solid #878787;
	width:auto;
}

.smallfield .header {
	display: block;
	font-weight: bold;
	color: rgb(73,102,145);
	font-size: 12pt;
	margin-bottom: 2px;
	line-height: 14pt;
}

.smallfield input {
	width: 95%;
	position: absolute;
	height: 30px;
	margin-right:5px;
	right: auto;
}

.smallfield:first-child input {
	-webkit-border-top-right-radius: 2px;
}

.textbox textarea {
	font-size: 17px;
	-webkit-appearance: none;
	border: none;
	font-weight: bold;
	font-size: 17px;
	width: 100%;
}

.graytitle {
	display: block;
}

.smallfield:last-child input {
	-webkit-border-bottom-right-radius: 2px;
}
.bigButton{
	font-size: 20pt;
	font-weight:bold;
	width:auto;
	height:px;
	margin: 3px 9px 17px;
	-webkit-border-top-left-radius: 12px;
	-webkit-border-bottom-left-radius: 12px;
	border-width: 0 12px 0 12px;
	-webkit-border-top-right-radius: 12px;
	-webkit-border-bottom-right-radius: 12px;
}
a.bigButton{
	display: block;
	color: #fff;
	text-shadow: rgba(0,0,0,0.6) 0 -1px 0;
	text-decoration: none;
	text-align:center;
	padding:10px 0 10px 0;
}
a.bigButton.white{
	color: #000;
	text-shadow: rgba(255,255,255,0.6) 0 2px 0;
}
.bigButton.blue{
	-webkit-border-image:url("../images/webkit/big-button-select.png") 0 12 0 12; !important
}
.bigButton.white {
	-webkit-border-image:url("../images/webkit/big-button-white.png") 0 12 0 12;
}
.bigButton.grey {
	-webkit-border-image:url("../images/webkit/big-button-grey.png") 0 12 0 12;
}
.bigButton.red:active{
	-webkit-border-image:url("../images/webkit/big-button-red-touch.png") 0 12 0 12; !important
}
.bigButton.red {
	-webkit-border-image:url("../images/webkit/big-button-red.png") 0 12 0 12;
}
.bigButton.green:active{
	-webkit-border-image:url("../images/webkit/big-button-green-touch.png") 0 12 0 12; !important
}
.bigButton.green {
	-webkit-border-image:url("../images/webkit/big-button-green.png") 0 12 0 12;
}

.checkbox .name {
	color: rgb(73,102,145);
}