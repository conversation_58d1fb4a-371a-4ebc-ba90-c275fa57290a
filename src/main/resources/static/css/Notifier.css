@CHARSET "ISO-8859-1";
#Notifier{
	bottom:0px;
	position:relative;
	float:right;
	
	background-image: url('../images/system/notify_v2.svg');
	background-repeat:no-repeat;
    background-size:contain;
    height: 40px;
	width:45px;	
}

#notifierCounter{
	position:relative;
	float:right;
	margin-right:-15%;
	margin-top:-20%;
	
	background-image: url('../images/system/notify_spot.svg');
	background-repeat:no-repeat;
	background-size:contain;
	height: 25px;
	width:25px;	
}

#notifierCounterText{
	position:relative;
	float:right;
	margin-top:3px;
	margin-right:6.8px;
	color:black;
	font-weight: bold;
	text-align: center;
}



#notificationHead{
	background:#098878;
	height: 2.4em;
	width: 100%
}

#bigNotification{
	font-family:"Arial", "Times New Roman", Times, serif;
	color:#fff;
	font-size:2.0em;
	font-style:oblique;
}

.notifyError {
      color: #B94A48;
      background-color: #F2DEDE;
      border-color: #EED3D7;
 	}
    
.notifySuccess {
      color: #468847;
      background-color: #DFF0D8;
      border-color: #D6E9C6;
	 }
    
.notifyInfo {
      color: #3A87AD;
      background-color: #D9EDF7;
      border-color: #BCE8F1;
    }
    
.notifyWarn {
      color: #C09853;
      background-color: #FCF8E3;
      border-color: #FBEED5;
    }