#wrapper{    
		position:absolute;
		height:690px;
		width: 950px;
		max-height:650px;
		left:50%;
    	top:50%;
		margin:-325px 0 0 -475px;   	
}

#wrapper_home{    
		position:relative;
		height:520px;
		width: auto; 
		max-height:650px;
		left:50%;
    	top:285px;
		margin:-225px 0 0 -475px;  	
}

#infoBar{
	position:absolute;
	background:#000000;
	height:10%;
	width: 100%;
}

#adminBox{
	position:absolute;
	height: 60px;
	width: 143px;
	margin-left: 785px;	
	color:#FFF;
}

#lastviewed{	
	position:absolute;
	background-color:#66F;
	margin-top:45px;
	margin-left: 700px;
	height:280px;
	width:230px;
	overflow:auto;
	
	 /* fallback */ 
	 background-color: #383939; 
	 background-image: url(images/linear_bg_1.png); 
	 background-repeat: repeat-y; 
	 /* Safari 4-5, Chrome 1-9 */ 
	 background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#383939), to(#787777)); 
	 /* Safari 5.1, Chrome 10+ */ 
	 background: -webkit-linear-gradient(top, #383939, #787777); 
	 /* Firefox 3.6+ */ background: -moz-linear-gradient(top, #383939, #787777); 
	 /* IE 10 */ 
	 background: -ms-linear-gradient(top, #383939, #787777); 
	 /* Opera 11.10+ */ 
	 background: -o-linear-gradient(top, #383939, #787777);
}

.listfilm{
	font-size:67%;
}

div.highlight:hover {
	filter: alpha(opacity=50);
	opacity:0.5;
}

#changeToBlue{
	background:BLUE;
}
#changeToGreen{
	background:GREEN;
}

#changeToYellow{
	background:#a744f7;
}

.rotatedText{
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
  display:block;
  position:relative;
  right:8%;
  top:50%;
  color:white;
}

.logo{
	height: 5.5em;
	width: 12em;
	position:fixed;
}

html {
	background: url(../images/system/home-bak.png) no-repeat center center fixed;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: cover;
	filter: alpha(opacity=85);
	opacity: .85;
}

#playerArea {
	position:absolute;
	height:35%;
	width:35%;
	
	margin-top:35px;
	margin-left:10%;
	
	background-color:green;
}

#dateTime{
	font-family: Helvetica; 
    font-style:italic;
    
    font-size: 78%
}

#infoTxt{
	height:100px;
	width:300px;
	background-color: #d1d4d3;
	overflow: hidden;
	position: absolute;
	
   white-space: pre-wrap;      /* CSS3 */   
   white-space: -moz-pre-wrap; /* Firefox */    
   white-space: -pre-wrap;     /* Opera <7 */   
   white-space: -o-pre-wrap;   /* Opera 7 */    
   word-wrap: break-word;      /* IE */
}

h4{
	font-family: Helvetica; 
    font-style:italic;
	font-Size:18px;
	font-style: bold;
	font-color:000000;
	color: #9F9F9F;
	background:#6d6e65;
	
	padding-botton: .4em 1em;
	margin: 0 0 .5em 0;
	overflow: hidden;
	position: absolute;
	border-bottom: 1px solid #BFBFBF;
	border-top: 1px solid black;
}

#searchOptions {
	position: absolute;
	height:45px;
	width:22%;
	top:16%;
	right:6%;
	
}

#statsArea{
	position: absolute;
	height: 100px;
	width: 200px;
	
	background:#6d6e65;
}

#shade_area{
	position: absolute;
	height: 100%;
	width: 100%;
	
	background:black;
	opacity: 0.75;
	visibility:hidden;
}

#welcome{
	color:#FFF;
	font-size: 2em;
	font-style:italic;
}

#footer{
	position:absolute;
	height:40px;
	width:950px;
	margin-top:592px;
	text-align:center;
	font-size: .7em;
	color:#FFF;
}

#nav{
	position:absolute;
	display: inline;
	margin-top: 135px;
	height: 18px;
	width: 950px;
	color: #FFF;
	overflow:none;
}
			
#QLinks{
	position:absolute;
	height:430px;
	width:100px;
	margin-top:45px;
	margin-left:10px;
	color: #FFF;
	
	 /* fallback */ 
	 background-color: #383939; 
	 background-image: url(images/linear_bg_1.png); 
	 background-repeat: repeat-y; 
	 /* Safari 4-5, Chrome 1-9 */ 
	 background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#383939), to(#787777)); 
	 /* Safari 5.1, Chrome 10+ */ 
	 background: -webkit-linear-gradient(left, #383939, #787777); 
	 /* Firefox 3.6+ */ background: -moz-linear-gradient(left, #383939, #787777); 
	 /* IE 10 */ 
	 background: -ms-linear-gradient(left, #383939, #787777); 
	 /* Opera 11.10+ */ 
	 background: -o-linear-gradient(left, #383939, #787777);	
}

table {
	color: #FFF;
}


#scroller{	
	position:absolute;
	margin-top:386px;
	margin-left: 130px;
	height:90px;
	width:750px;
}

#info{
	position:absolute;
	height:42px;
	width:550px;
	margin-left:130px;
	margin-top:443px;
	color:#FFF;

	 /* fallback */ 
	 background-color: #383939; 
	 background-image: url(images/linear_bg_1.png); 
	 background-repeat: repeat-y; 
	 /* Safari 4-5, Chrome 1-9 */ 
	 background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#383939), to(#787777)); 
	 /* Safari 5.1, Chrome 10+ */ 
	 background: -webkit-linear-gradient(left, #383939, #787777); 
	 /* Firefox 3.6+ */ background: -moz-linear-gradient(left, #383939, #787777); 
	 /* IE 10 */ 
	 background: -ms-linear-gradient(left, #383939, #787777); 
	 /* Opera 11.10+ */ 
	 background: -o-linear-gradient(left, #383939, #787777);
}

#homeHead{
	background:#5f07a5;
	height: 2.4em;
	width: 100%
}

#bigHome{
	font-family:"Arial", "Times New Roman", Times, serif;
	color:#c484f7;
	font-size:2.0em;
	font-style:oblique;
}

#bigHome2{
	font-family:"Arial", "Times New Roman", Times, serif;
	color:#c484f7;
	font-size:100%;
	font-style:oblique;
}