.mbr-gallery .icon {
  z-index: 100 !important;
}

.mbr-gallery .mbr-gallery-item>div:hover .icon-video,
.mbr-gallery .mbr-gallery-item>div:hover .icon-focus {
  opacity: 1;
}

.mbr-gallery .icon-video {
  color: white;
  opacity: 0;
  position: absolute;
  top: calc(50% - 24px);
  left: calc(50% - 27px);
  font-size: 3rem;
  transition: .2s opacity ease-in-out;
  z-index: 5;
}

.mbr-gallery .icon-video::before {
  color: white;
  content: "\e011";
  position: absolute;
}

.mbr-gallery .icon-focus {
  color: white;
  opacity: 0;
  position: absolute;
  top: calc(50% - 24px);
  left: calc(50% - 32px);
  font-size: 3rem !important;
  transition: .2s opacity ease-in-out;
  z-index: 5;
  font-family: 'et-line' !important;
}

.mbr-gallery .icon-focus::before {
  content: '\e01b';
  color: white;
  position: absolute;
}

.mbr-gallery .mbr-gallery-item {
  cursor: pointer;
}

.mbr-gallery .item-overlay {
  position: absolute;
  width: 60px;
  font-size: 60px;
  z-index: 10;
  height: 60px;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
  cursor: pointer;
  z-index: 5;
}

.mbr-gallery .mbr-gallery-item> div::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: -webkit-linear-gradient(left, #554346, #45505b) !important;
  background: -moz-linear-gradient(left, #554346, #45505b) !important;
  background: -o-linear-gradient(left, #554346, #45505b) !important;
  background: -ms-linear-gradient(left, #554346, #45505b) !important;
  background: linear-gradient(left, #554346, #45505b) !important;
  opacity: 0;
  -webkit-transition: .2s opacity ease-in-out;
  transition: .2s opacity ease-in-out;
}

.mbr-gallery .mbr-gallery-item:hover> div::before {
  opacity: 0.5;
}

.mbr-gallery-row {
  padding-left: 0px;
  padding-right: 0px;
}

.mbr-gallery-item__hided {
  position: absolute !important;
  width: 0px !important;
  height: 0px;
  left: 0 !important;
  padding: 0 !important;
}

.mbr-gallery-item__hided img {
  display: none;
}

.mbr-gallery-item__hided span {
  display: none;
}

.mbr-gallery-filter {
  padding-top: 30px;
  padding-bottom: 30px;
  text-align: center;
}
.mbr-gallery-filter.gallery-filter__bg li{
 color: white;
}
.mbr-gallery-filter.gallery-filter__bg .active{
  color:black;
  background-color: white;
}

.mbr-gallery-filter ul {
  padding-left: 0px;
  display: inline-block;
  list-style: none;
  margin-bottom: 0px;
}

.mbr-gallery-filter li {
  font-size: 16px;
  font-family: 'Raleway';
  cursor: pointer;
  color: #242424;
  display: inline-block;
  padding: 9px 30px 9px 30px;
  border: 1px solid #e6e6e6;
  transition: all 0.3s ease-out;
}

.mbr-gallery-filter li:hover {
  background-color: #333333;
  color: #c39f76;
}

.mbr-gallery-filter li.active {
  color: white;
  background-color: #333333;
}

.mbr-gallery-filter li.active:hover {
  color: white;
  background-color: #333333;
}

.mbr-gallery-item> div {
  position: relative;
}

.mbr-gallery-item--p1 {
  padding: 1rem;
}

.mbr-gallery-item--p2 {
  padding: 2rem;
}

.mbr-gallery-item--p3 {
  padding: 3rem;
}

.mbr-gallery-item--p4 {
  padding: 4rem;
}

.mbr-gallery-item--p5 {
  padding: 5rem;
}

.mbr-gallery-item--p6 {
  padding: 6rem;
}
.mbr-gallery .mbr-gallery-item--p6, .mbr-gallery .mbr-gallery-item--p5,.mbr-gallery .mbr-gallery-item--p4{
    width:50%;
}
@media(max-width:992px){
     .mbr-gallery-item--p1 {
    padding: 1rem;
  }
  .mbr-gallery-item--p2 {
    padding: 2rem;
  }
  .mbr-gallery-item--p3 {
    padding: 3rem;
  }
  .mbr-gallery-item--p4 {
    padding: 3rem;
  }
  .mbr-gallery-item--p5 {
    padding: 3rem;
  }
  .mbr-gallery-item--p6 {
    padding: 3rem;
  }
}
.video-container .mbr-background-video iframe {
  width:100%;
  height:100%;
}
@media(max-width:992px) and (min-width:400px) {

  .mbr-gallery .mbr-gallery-item--p6, .mbr-gallery .mbr-gallery-item--p5, .mbr-gallery .mbr-gallery-item--p4, .mbr-gallery .mbr-gallery-item--p3, .mbr-gallery .mbr-gallery-item--p2 {
    width: 50%;
  }
}
@media(max-width:400px){
    .mbr-gallery .mbr-gallery-item--p6, .mbr-gallery .mbr-gallery-item--p5, .mbr-gallery .mbr-gallery-item--p4, .mbr-gallery-item--p3{
    width:100%;
}
}