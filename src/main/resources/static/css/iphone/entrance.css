@charset "utf-8";
/* CSS Document */

#wrapper{    
    height: 90%;
	width: 90%;
	position: absolute;
	/* Center align this Div half of remaining height and width [(100 - 90) / 2]*/
   	top: 5%;
   	left: 5%;
}

body {
	background: url(http://192.168.0.2:8080/paymetv/images/system/entrance_screen.png) no-repeat center center fixed;
	background-size: 75% 98%;
	background-color: black;
	width: 100%;	
	}

@media screen and (max-device-width: 480px) and (orientation: landscape){
body {
	background: url(http://192.168.0.2:8080/paymetv/images/system/entrance_screen.png) no-repeat center center fixed;
	background-size: 75% 98%;
	background-color: black;
	width: 100%;	
	}
}

@media screen and (max-device-width: 320px) and (orientation: portrait){
body {
	background: url(http://192.168.0.2:8080/paymetv/images/system/entrance_screen.png) no-repeat center center fixed;
	background-size: 98% 85%;
	background-color: black;
	width: 100%;	
	}
}

/******  <PERSON><PERSON>an ********/
#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 24%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:1.7em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}
}

@media screen and (max-device-width: 640px) and (orientation: portrait){
#slogan{
	height:6%;
	width:99%; 
	position:absolute;
	top: 26%;
	font-family:Verdana, Geneva, sans-serif;
	font-size:2.7em;
	color:#FFF;
	text-align:center;
	text-shadow:#000 3px 3px 0px;
	}
}

/******  Enter ********/
@media screen and (max-device-width: 1136px) and (orientation: landscape){
#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:7%;
	height:5%;
	font-size:1.2em;
	color:#FFF;
	
	position: absolute;
   	top: 52%;
   	left: 50%;
   	margin-top: 1.4%; /* Half the height */
   	margin-left: -4.25%; /* Half the width */		
		
	text-align:center;

	display:block;
        -webkit-transform: rotate(12deg);
        -webkit-transform:margin-top(20%); /* Half the height */
   		-webkit-transform:margin-left(-4.25%); /* Half the width */
        
        -moz-transform: rotate(12deg); 
		-moz-transform: margin-top(2%); /* Half the height */
   		-moz-transform: margin-left(-4.25%); /* Half the width */
                
	}
}

@media screen and (max-device-width: 640px) and (orientation: portrait){
#enter{
	position:relative;
	font-family:Verdana, Geneva, sans-serif;
	width:13%;
	height:5%;
	font-size:1.5em;
	color:#FFF;
	
	position: absolute;
   	top: 54%;

   	margin-left: 43%;
	text-align:center;
	}
}

/******  CopyRight ********/
@media screen and (max-device-width: 1136px) and (orientation: landscape){
#copyright{
	font-family:Verdana, Geneva, sans-serif;
	font-size:14px;
	color:#FFF;
	
    position:absolute;
    bottom:5px;
    right:10px;
    width: 255px;
	}
}

@media screen and (max-device-width: 640px) and (orientation: portrait){
#copyright{
	font-family:Verdana, Geneva, sans-serif;
	font-size:18px;
	color:#FFF;
	
    position:absolute;
    bottom:5px;
    right:10px;
    width: 350px;
	}
}	

a:link {color: #FFF; text-decoration: none; }
a:active {color: #FFF; text-decoration: underline; }
a:visited {color: #CCC; text-decoration: underline; }
a:hover {color: #FFF; text-decoration: underline;}

#flags {
    position:absolute;
    top:0;
    left:3px;	
	}

#infoTXT{
	font-family:Verdana, Geneva, sans-serif;
	font-size:10px;
	color:#FFF;
	}

/* same style a copyright */
#footer{
	font-family:Verdana, Geneva, sans-serif;
	font-size:10px;
	color:#FFF;
	
    position:absolute;
    bottom:5px;
    right:10px;
	}
	

#signReg{
	position:absolute;
    top:0;
    left:3px;	
    margin-top:22px;
    font-size:10px;
	color:#FFF;
}

#content-enter{
	content_mast {padding: 0 8px 4px 8px;
	border-bottom: 1px solid #e6e6e6;
	height:112px;
	background-color:#00B;
}
