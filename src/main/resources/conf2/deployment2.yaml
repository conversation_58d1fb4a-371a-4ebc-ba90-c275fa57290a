apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: load-balancer-example
  name: pmtv-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app.kubernetes.io/name: load-balancer-example
  template:
    metadata:
      labels:
        app.kubernetes.io/name: load-balancer-example
    spec:
      containers:
        # Main application container
        - name: pmtv-app
          image: paymetv-app
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8084
          volumeMounts:
            - name: certbot-certs
              mountPath: /etc/letsencrypt  # Shared volume for certificates

        # Certbot container for SSL/TLS certificates
        - name: certbot
          image: certbot/certbot
          args:
            - certonly
            - --webroot
            - -w
            - /var/www/certbot
            - --email
            - <EMAIL>
            - --agree-tos
            - --no-eff-email
            - -d
            - www.paymetv.co.uk
          volumeMounts:
            - name: certbot-certs
              mountPath: /etc/letsencrypt  # Shared certificate volume
            - name: certbot-webroot
              mountPath: /var/www/certbot  # Webroot for HTTP challenge

      # Volumes shared between the app and Certbot
      volumes:
        - name: certbot-certs
          emptyDir: {}  # Temporary storage for certs (use PersistentVolumeClaim for persistence)
        - name: certbot-webroot
          emptyDir: {}  # Temporary storage for webroot validation
