apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: load-balancer-example
  name: pmtv-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app.kubernetes.io/name: load-balancer-example
  template:
    metadata:
      labels:
        app.kubernetes.io/name: load-balancer-example
    spec:
      containers:
        - name: pmtv-app
          image: paymetv/paymetv-app:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 80
      volumes:
        - name: certs
          secret:
            secretName: paymetv-tls-secret  # Refers to the static certificate secret