apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  labels:
    app: pmtv-app
  name: paymetv.co.uk
spec:
  rules:
    - host: paymetv.co.uk
      http:
        paths:
          - backend:
              service:
                name: pmtv-app
                port:
                  number: 80
            path: /
            pathType: Prefix
tls:
  - hosts:
      - paymetv.co.uk # your domain
    secretName: letsencrypt-prod # secret name, same as the privateKeySecretRef in the (Cluster)Issuer