apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  labels:
    app: paymetv-app
  name: pmtv-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  rules:
    - host: paymetv.co.uk
      http:
        paths:
          - backend:
              service:
                name: pmtv-service
                port:
                  number: 80 # use appropriate port
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - paymetv.co.uk
      secretName: letsencrypt-prod # secret name, same as the privateKeySecretRef in the (Cluster)Issuer