apiVersion: batch/v1
kind: Job
metadata:
  name: certbot
spec:
  template:
    spec:
      containers:
        - name: certbot
          image: certbot/certbot
          args: ["certonly", "--webroot", "-w", "/var/www/certbot", "--email", "<EMAIL>", "--agree-tos", "--no-eff-email", "-d", "paymetv.co.uk"]
          volumeMounts:
            - mountPath: /var/www/certbot
              name: webroot
            - mountPath: /etc/letsencrypt
              name: letsencrypt
      restartPolicy: OnFailure
      volumes:
        - name: webroot
          emptyDir: {}
        - name: letsencrypt
          persistentVolumeClaim:
            claimName: letsencrypt-pvc