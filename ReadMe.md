[![Build Status](http://www.paymetv.co.uk:8080/buildStatus/icon?job=paymetv-pipeline)](http://www.paymetv.co.uk:8080/job/paymetv-pipeline/)
##Paymetv limited
###The fast track way to get paid for your videos


####Deploy/Setup
````
mvn package && java -jar target/paymetv-0.0.1-SNAPSHOT.jar
docker build -t paymetv-app:latest .
docker run -dp ***********:80:8080 paymetv-app:latest
````
----
````
docker run -p 8080:8080 -p 50000:50000 -d -v jenkins_home:/var/jenkins_home jenkins/jenkins:lts
usr: jenkins
pwd: jenkins1

------
Get initalAdminPassword
------
docker exec -it <container_id> sh
````

----
````
Example:
https://medium.com/@luca.milan/free-https-certificates-with-lets-encrypt-and-certbot-with-docker-1f44e7b103c6

certbot SSL
run new-dns-challenge script
copy hash into LCN - DNS Administration
https://admin.lcn.com/dns/mod.php?message_header=THANK+YOU&message=VGhlIGZvbGxvd2luZyBETlMgem9uZXMgaGF2ZSBiZWVuIGNyZWF0ZWQvdXBkYXRlZC4gUGxlYXNlIGFsbG93IDI0LTQ4IGhvdXJzIGZvciB0aGVzZSBjaGFuZ2VzIHRvIHRha2UgZWZmZWN0LiAtLTY1NDA4ZTNhOWUxMmQ5OTRjOWJmZjIxMWQ5ZmE1ZWQ4&domain=paymetv.co.uk&origin=paymetv.co.uk&ns=lcn.com
add hash to the box with Host name = _acme-challenge

------
Check that the DNS TXT has been published using link below:
https://toolbox.googleapps.com/apps/dig/#ANY/_acme-challenge.paymetv.co.uk
------
``

------
== HACK ==
Try to output the pem to console (pod gets destroyed after execution)
------
try this issuer solution
https://dev.to/ileriayo/adding-free-ssltls-on-kubernetes-using-certmanager-and-letsencrypt-a1l
````