#!/bin/bash

echo "${PWD}/etc-letsencrypt:/etc/letsencrypt"


domain="paymetv.co.uk"
email="<EMAIL>"

# Run Certbot to obtain the certificate using DNS challenge
#docker run -it --rm --name certbot \   <-- removing the container after it's done -->
docker run -it --name certbot \
    -v "${PWD}/etc-letsencrypt:/etc/letsencrypt" \
    -v "${PWD}/var-lib-letsencrypt:/var/lib/letsencrypt" \
    certbot/certbot certonly \
    --manual \
    --preferred-challenges dns \
    --email "$email" \
    --agree-tos \
    --domain "$domain" \
    --rsa-key-size 2048
        cat "${PWD}/etc-letsencrypt/live/$domain/fullchain.pem" && \
        cat "##########################" && \
        cat "${PWD}/etc-letsencrypt/live/$domain/privkey.pem"

        cat "${PWD}/etc-letsencrypt:/etc/letsencrypt/fullchain.pem" && \
        cat "##########################" && \
        cat "${PWD}/var-lib-letsencrypt:/var/lib/letsencrypt/privkey.pem"